[{"name": "完整Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3395, "error": "HTTP 403", "response_time": 3.7481796741485596, "has_product_data": false}, {"name": "完整Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 4.567872524261475, "has_product_data": false}, {"name": "完整Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3395, "error": "HTTP 403", "response_time": 2.651331663131714, "has_product_data": false}, {"name": "完整Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 3.1444008350372314, "has_product_data": false}, {"name": "完整Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 5.773515224456787, "has_product_data": false}, {"name": "完整Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3256, "error": "HTTP 403", "response_time": 10.471137285232544, "has_product_data": false}, {"name": "仅认证Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3395, "error": "HTTP 403", "response_time": 5.515073299407959, "has_product_data": false}, {"name": "仅认证Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 2.997124433517456, "has_product_data": false}, {"name": "仅认证<PERSON><PERSON> + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3395, "error": "HTTP 403", "response_time": 3.858726739883423, "has_product_data": false}, {"name": "仅认证Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 3.802032947540283, "has_product_data": false}, {"name": "仅认证Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3348, "error": "HTTP 403", "response_time": 3.7503662109375, "has_product_data": false}, {"name": "仅认证<PERSON>ie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3256, "error": "HTTP 403", "response_time": 6.432692050933838, "has_product_data": false}, {"name": "仅会话Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 5.871463775634766, "has_product_data": false}, {"name": "仅会话Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 4.8215248584747314, "has_product_data": false}, {"name": "仅会话<PERSON><PERSON> + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 3.464285135269165, "has_product_data": false}, {"name": "仅会话Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 8.817590951919556, "has_product_data": false}, {"name": "仅会话Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.608052968978882, "has_product_data": false}, {"name": "仅会话<PERSON>ie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3208, "error": "HTTP 403", "response_time": 5.559196472167969, "has_product_data": false}, {"name": "仅安全Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 6.769287586212158, "has_product_data": false}, {"name": "仅安全Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.515037775039673, "has_product_data": false}, {"name": "仅安全Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 3.0819687843322754, "has_product_data": false}, {"name": "仅安全Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.7174150943756104, "has_product_data": false}, {"name": "仅安全Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 4.468802213668823, "has_product_data": false}, {"name": "仅安全Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3208, "error": "HTTP 403", "response_time": 5.8414530754089355, "has_product_data": false}, {"name": "最小化Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 4.542093515396118, "has_product_data": false}, {"name": "最小化Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 5.053301095962524, "has_product_data": false}, {"name": "最小化Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 3.8477213382720947, "has_product_data": false}, {"name": "最小化Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 6.780618190765381, "has_product_data": false}, {"name": "最小化Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.332995891571045, "has_product_data": false}, {"name": "最小化Cookie + 仅Ozon请求头", "success": false, "status_code": 403, "response_size": 3208, "error": "HTTP 403", "response_time": 3.5984604358673096, "has_product_data": false}, {"name": "无Cookie + 完整请求头", "success": false, "status_code": null, "response_size": 0, "error": "请求超时", "response_time": 0, "has_product_data": false}, {"name": "无Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 5.235147714614868, "has_product_data": false}, {"name": "无Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 3.37553071975708, "has_product_data": false}, {"name": "无Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.7985973358154297, "has_product_data": false}, {"name": "无Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.5246973037719727, "has_product_data": false}, {"name": "无Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3208, "error": "HTTP 403", "response_time": 6.069284677505493, "has_product_data": false}, {"name": "核心Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 5.50412392616272, "has_product_data": false}, {"name": "核心Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 3.5644965171813965, "has_product_data": false}, {"name": "核心Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 3347, "error": "HTTP 403", "response_time": 2.9800539016723633, "has_product_data": false}, {"name": "核心Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 7.380649089813232, "has_product_data": false}, {"name": "核心Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 3300, "error": "HTTP 403", "response_time": 4.30363130569458, "has_product_data": false}, {"name": "核心Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 3208, "error": "HTTP 403", "response_time": 3.56894588470459, "has_product_data": false}]