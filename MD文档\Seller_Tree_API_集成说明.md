# 🌳 Seller Tree API 集成说明

## 📋 概述

本次更新集成了 Ozon Seller Tree API (`https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku`)，用于获取更准确的商品类目信息。这个 API 可以通过商品 SKU 直接获取官方的类目分类信息，大大提高了类目确定的准确性。

## 🎯 主要改进

### 1. **新增 Seller Tree API 支持**
- 通过 SKU 直接获取官方类目信息
- 支持多级类目（Level 2, 3, 4）
- 获取准确的 `description_type_id`

### 2. **智能 SKU 提取**
- 从商品 ID 字段提取
- 从商品 URL 中解析
- 支持多种 SKU 字段格式

### 3. **优化的类目确定流程**
- 优先使用 Seller Tree API
- 失败时回退到传统推断方法
- 提供详细的日志记录

## 🔧 技术实现

### API 响应格式
```json
{
  "resolved_categories_by_sku": {
    "2045588660": {
      "description_category_id_level_2": "17027488",
      "description_category_id_level_3": "17028973",
      "description_category_id_level_4": "17029258",
      "description_type_id": "92840"
    }
  }
}
```

### 新增方法

#### `OzonApiClient.get_category_by_sku(sku: str)`
通过 SKU 获取类目信息
- **参数**: `sku` - 商品 SKU
- **返回**: 类目信息字典或 None
- **功能**: 调用 Seller Tree API 获取准确的类目数据

#### `OzonApiClient.extract_sku_from_scraped_data(scraped_data: dict)`
从抓取数据中提取 SKU
- **参数**: `scraped_data` - 抓取的商品数据
- **返回**: 提取的 SKU 字符串或 None
- **功能**: 智能提取商品 SKU

#### `OzonApiClient.get_category_info_from_scraped_data(scraped_data: dict)`
从抓取数据获取完整类目信息
- **参数**: `scraped_data` - 抓取的商品数据
- **返回**: (category_id, type_id) 元组或 None
- **功能**: 完整的类目获取流程

## 🚀 使用方法

### 1. **在 main.py 中的使用**
```python
# 确定商品类目
print("🔍 确定商品类目...")

# 首先尝试通过 Seller Tree API 获取准确的类目信息
category_result = api_client.get_category_info_from_scraped_data(scraped_data)

if category_result:
    category_id, type_id = category_result
    print(f"✅ 通过 Seller Tree API 确定类目: ID={category_id}, Type={type_id}")
else:
    # 回退到原有的类目推断逻辑
    print("⚠️ Seller Tree API 获取类目失败，使用传统方法推断类目...")
    # ... 原有逻辑
```

### 2. **独立使用 API**
```python
from modules.api_client import OzonApiClient

# 创建客户端
api_client = OzonApiClient(client_id, api_key, base_url)

# 通过 SKU 获取类目
category_info = api_client.get_category_by_sku("2045588660")

if category_info:
    print(f"类目信息: {category_info}")
```

### 3. **从抓取数据获取类目**
```python
# 假设已有抓取的数据
scraped_data = {
    "id": "2045588660",
    "name": "商品名称",
    "url": "https://www.ozon.ru/product/name-2045588660/"
}

# 获取类目信息
result = api_client.get_category_info_from_scraped_data(scraped_data)
if result:
    category_id, type_id = result
    print(f"Category ID: {category_id}, Type ID: {type_id}")
```

## 🧪 测试验证

### 运行集成测试
```bash
python test_seller_tree_integration.py
```

测试包括：
1. **SKU 提取测试** - 验证从不同数据源提取 SKU
2. **Seller Tree API 测试** - 验证 API 调用功能
3. **类目信息提取测试** - 验证完整的提取流程
4. **完整集成测试** - 验证端到端的功能

### 预期输出
```
🧪 Seller Tree API 集成测试
============================================================
✅ 配置验证通过

📋 开始测试...

🔧 测试 SKU 提取功能...
📋 测试用例: 直接包含 ID
✅ 成功提取 SKU: 2045588660

🔧 测试 Seller Tree API 调用...
测试 SKU: 2045588660
✅ Seller Tree API 调用成功
类目信息: {'description_category_id_level_2': '17027488', ...}

🔧 测试类目信息提取...
✅ 成功获取类目信息:
  Category ID: 17029258
  Type ID: 92840

🎉 所有测试通过！Seller Tree API 集成成功
```

## 🔍 工作流程

### 新的类目确定流程：

1. **抓取商品数据** - 获取商品基本信息
2. **提取 SKU** - 从多个可能的字段中智能提取
3. **调用 Seller Tree API** - 通过 SKU 获取官方类目信息
4. **解析类目数据** - 选择最具体的类目级别
5. **回退机制** - API 失败时使用传统推断方法

### 类目级别优先级：
1. `description_category_id_level_4` (最具体)
2. `description_category_id_level_3`
3. `description_category_id_level_2` (最一般)

## ⚠️ 注意事项

### 1. **Cookie 依赖**
- Seller Tree API 需要有效的 Ozon 卖家 Cookie
- 使用最小化 Cookie（仅 abt_data）
- 需要定期更新 Cookie

### 2. **错误处理**
- API 调用失败时自动回退
- 详细的错误日志记录
- 网络超时和重试机制

### 3. **性能考虑**
- API 调用有一定延迟
- 建议缓存类目信息
- 避免频繁调用

## 🔧 配置要求

### 环境变量
确保 `.env` 文件包含：
```
OZON_SESSION_COOKIE="your_cookie_here"
OZON_CLIENT_ID="your_client_id"
OZON_API_KEY="your_api_key"
```

### 依赖包
```
curl-cffi>=0.5.0
```

## 📊 优势对比

| 方法 | 准确性 | 速度 | 可靠性 | 维护成本 |
|------|--------|------|--------|----------|
| Seller Tree API | 🟢 高 | 🟡 中等 | 🟢 高 | 🟡 中等 |
| 传统推断 | 🟡 中等 | 🟢 快 | 🟡 中等 | 🔴 高 |
| 手动选择 | 🟢 高 | 🔴 慢 | 🟢 高 | 🔴 高 |

## 🚀 后续优化

### 计划中的改进：
1. **缓存机制** - 缓存已查询的 SKU 类目信息
2. **批量查询** - 支持一次查询多个 SKU
3. **类目映射** - 建立本地类目映射数据库
4. **智能推荐** - 基于历史数据推荐类目

## 📞 故障排除

### 常见问题：

1. **API 返回 403 错误**
   - 检查 Cookie 是否有效
   - 更新 abt_data 值

2. **未找到 SKU**
   - 检查商品 URL 格式
   - 验证抓取数据完整性

3. **类目信息不完整**
   - 检查 API 响应格式
   - 验证 SKU 是否存在于 Ozon

通过这个集成，现在可以获得更准确、更可靠的商品类目信息，大大提高了自动跟卖的成功率。
