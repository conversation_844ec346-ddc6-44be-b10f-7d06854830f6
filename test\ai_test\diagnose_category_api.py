#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断类目 API 问题的程序
检查 Seller Tree API 数据和正确的 API 调用格式
"""

import sys
import os
from typing import Dict, List, Optional
import json
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.logger import api_logger
import config

def test_correct_api_format(api_client: OzonApiClient, category_data: Dict) -> Dict:
    """
    使用正确的 API 格式测试类目属性获取
    根据 API 文档，参数名应该是 description_category_id 而不是 category_id
    """
    results = {}
    
    print("=" * 80)
    print("🔧 使用正确的 API 格式测试")
    print("=" * 80)
    
    # 从 Seller Tree API 数据中提取所有可能的组合
    test_cases = []
    
    # 测试用例 1: level_3 + type_id (推荐)
    if 'description_category_id_level_3' in category_data:
        test_cases.append({
            'description_category_id': int(category_data['description_category_id_level_3']),
            'type_id': int(category_data['description_type_id']),
            'description': f"Level 3 类目 ({category_data['description_category_id_level_3']}) + type_id ({category_data['description_type_id']})"
        })
    
    # 测试用例 2: level_4 + type_id
    if 'description_category_id_level_4' in category_data:
        test_cases.append({
            'description_category_id': int(category_data['description_category_id_level_4']),
            'type_id': int(category_data['description_type_id']),
            'description': f"Level 4 类目 ({category_data['description_category_id_level_4']}) + type_id ({category_data['description_type_id']})"
        })
    
    # 测试用例 3: level_2 + type_id
    if 'description_category_id_level_2' in category_data:
        test_cases.append({
            'description_category_id': int(category_data['description_category_id_level_2']),
            'type_id': int(category_data['description_type_id']),
            'description': f"Level 2 类目 ({category_data['description_category_id_level_2']}) + type_id ({category_data['description_type_id']})"
        })
    
    for i, test_case in enumerate(test_cases, 1):
        description_category_id = test_case['description_category_id']
        type_id = test_case['type_id']
        description = test_case['description']
        
        print(f"\n📋 测试 {i}/{len(test_cases)}: {description}")
        print(f"   description_category_id: {description_category_id}")
        print(f"   type_id: {type_id}")
        print("-" * 60)
        
        try:
            # 使用正确的参数名调用 API
            payload = {
                "description_category_id": description_category_id,  # 正确的参数名
                "type_id": type_id,
                "language": "DEFAULT"
            }
            
            start_time = time.time()
            response = api_client._post("/v1/description-category/attribute", payload)
            duration = time.time() - start_time
            
            attributes = response.get("result", [])
            
            # 记录成功结果
            results[description_category_id] = {
                'status': 'success',
                'description': description,
                'type_id': type_id,
                'attributes_count': len(attributes),
                'duration': round(duration, 2),
                'sample_attributes': attributes[:3] if attributes else []
            }
            
            print(f"✅ 成功! 获取到 {len(attributes)} 个属性")
            print(f"⏱️  耗时: {duration:.2f}s")
            
            # 显示前几个属性的基本信息
            if attributes:
                print("📝 前3个属性示例:")
                for j, attr in enumerate(attributes[:3], 1):
                    attr_name = attr.get('name', 'N/A')
                    attr_id = attr.get('id', 'N/A')
                    is_required = attr.get('is_required', False)
                    required_text = "必填" if is_required else "可选"
                    print(f"   {j}. {attr_name} (ID: {attr_id}) - {required_text}")
            
        except Exception as e:
            # 记录失败结果
            error_msg = str(e)
            results[description_category_id] = {
                'status': 'error',
                'description': description,
                'type_id': type_id,
                'error': error_msg,
                'duration': round(time.time() - start_time, 2)
            }
            
            print(f"❌ 失败! 错误: {error_msg}")
        
        # 添加延迟避免请求过快
        if i < len(test_cases):
            print("⏳ 等待 1 秒...")
            time.sleep(1)
    
    return results

def get_seller_tree_data(api_client: OzonApiClient, sku: str) -> Optional[Dict]:
    """获取 Seller Tree API 数据"""
    try:
        print(f"🔍 获取 SKU {sku} 的 Seller Tree 数据...")
        category_info = api_client.get_category_by_sku(sku)
        
        if category_info:
            print("✅ Seller Tree 数据获取成功:")
            for key, value in category_info.items():
                print(f"   {key}: {value}")
            return category_info
        else:
            print("❌ 未能获取 Seller Tree 数据")
            return None
            
    except Exception as e:
        print(f"❌ 获取 Seller Tree 数据失败: {e}")
        return None

def test_category_tree_api(api_client: OzonApiClient):
    """测试类目树 API 以验证连接"""
    try:
        print("🌳 测试类目树 API...")
        tree = api_client.get_category_tree()
        print(f"✅ 类目树获取成功，共 {len(tree)} 个顶级类目")
        
        # 显示前几个类目作为示例
        if tree:
            print("📝 前3个顶级类目示例:")
            for i, category in enumerate(tree[:3], 1):
                name = category.get('title', 'N/A')
                cat_id = category.get('description_category_id', 'N/A')
                type_id = category.get('type_id', 'N/A')
                print(f"   {i}. {name} (category_id: {cat_id}, type_id: {type_id})")
        
        return True
    except Exception as e:
        print(f"❌ 类目树 API 测试失败: {e}")
        return False

def print_diagnosis_summary(results: Dict, category_data: Dict):
    """打印诊断结果摘要"""
    print("\n" + "=" * 80)
    print("📊 诊断结果摘要")
    print("=" * 80)
    
    print(f"\n📋 原始 Seller Tree 数据:")
    for key, value in category_data.items():
        print(f"   {key}: {value}")
    
    successful_tests = []
    failed_tests = []
    
    for category_id, result in results.items():
        if result['status'] == 'success':
            successful_tests.append((category_id, result))
        else:
            failed_tests.append((category_id, result))
    
    print(f"\n✅ 成功的测试: {len(successful_tests)}")
    for category_id, result in successful_tests:
        print(f"   • description_category_id: {category_id}")
        print(f"     描述: {result['description']}")
        print(f"     属性数量: {result['attributes_count']}, 耗时: {result['duration']}s")
    
    print(f"\n❌ 失败的测试: {len(failed_tests)}")
    for category_id, result in failed_tests:
        print(f"   • description_category_id: {category_id}")
        print(f"     描述: {result['description']}")
        print(f"     错误: {result['error']}")
    
    # 推荐结果
    if successful_tests:
        print(f"\n🎯 推荐使用的参数:")
        # 按属性数量排序，选择属性最多的
        best_result = max(successful_tests, key=lambda x: x[1]['attributes_count'])
        category_id, result = best_result
        print(f"   description_category_id: {category_id}")
        print(f"   type_id: {result['type_id']}")
        print(f"   描述: {result['description']}")
        print(f"   属性数量: {result['attributes_count']}")
        
        print(f"\n💡 修复建议:")
        print(f"   在您的代码中，将 get_category_attributes 方法的参数名从")
        print(f"   'category_id' 改为 'description_category_id'")
        print(f"   并使用推荐的 description_category_id: {category_id}")
    else:
        print(f"\n⚠️  所有测试都失败了")
        print(f"   可能的原因:")
        print(f"   1. API 权限问题")
        print(f"   2. type_id 无效")
        print(f"   3. 需要使用不同的 API 版本")

def main():
    """主程序"""
    try:
        # 验证配置
        print("🔧 验证配置...")
        config.validate_config()
        
        # 初始化API客户端
        print("🔧 初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        print("🔧 测试API连接...")
        if not api_client.test_api_connection():
            print("❌ API连接失败，请检查您的API凭证")
            return
        
        print("✅ API连接成功")
        
        # 测试类目树 API
        if not test_category_tree_api(api_client):
            print("⚠️  类目树 API 测试失败，但继续进行其他测试...")
        
        # 获取 Seller Tree 数据
        sku = "1921851629"  # 从您的日志中获取的 SKU
        category_data = get_seller_tree_data(api_client, sku)
        
        if not category_data:
            print("❌ 无法获取 Seller Tree 数据，无法继续测试")
            return
        
        # 使用正确的 API 格式进行测试
        results = test_correct_api_format(api_client, category_data)
        
        # 打印诊断摘要
        print_diagnosis_summary(results, category_data)
        
        # 保存结果
        output_data = {
            'seller_tree_data': category_data,
            'test_results': results,
            'timestamp': time.time()
        }
        
        with open('diagnosis_results.json', 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 诊断结果已保存到: diagnosis_results.json")
        print(f"\n🎉 诊断完成!")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
