{"timestamp": "2025-08-01T16:23:39.046328", "request_type": "get_category_attributes", "url": "https://api-seller.ozon.ru/v1/description-category/attribute", "request_data": {"description_category_id": 17028973, "type_id": 115944384}, "response_data": [{"id": 11254, "attribute_complex_id": 0, "name": "<PERSON>-конт<PERSON>нт JSON", "description": "Добавьте расширенное описание товара с фото и видео по шаблону в формате JSON. Подробнее о заполнении этой характеристики можно узнать в статье \"Rich-контент\" в \"Базе знаний\".", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 9024, "attribute_complex_id": 0, "name": "Код продавца", "description": "Цифро-буквенный код товара для его учета, является уникальным среди товаров. Не является EAN/серийным номером/штрихкодом, не равен названию модели товара - для этих параметров есть отдельные характеристики. Код продавца не выводится в карточке товара на сайте. Может использоваться при автоматическом формировании названия товара.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 7110, "attribute_complex_id": 0, "name": "Тематика фигурки", "description": "Выберите одно или несколько значений из списка, но не больше 6. В xls-файле варианты заполняются через точку с запятой (;) без пробелов.", "type": "String", "is_collection": true, "is_required": false, "is_aspect": false, "max_value_count": 6, "group_name": "", "group_id": 0, "dictionary_id": 733, "category_dependent": false, "complex_is_collection": false}, {"id": 7188, "attribute_complex_id": 0, "name": "Количество элементов, шт", "description": "Общее количество всех используемых в эксплуатации деталей. Можно указать только целое число.", "type": "Integer", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 85, "attribute_complex_id": 0, "name": "<PERSON>р<PERSON><PERSON>д", "description": "Наименование бренда, под которым произведен товар. Если товар не имеет бренда, используйте значение \"Нет бренда\".", "type": "String", "is_collection": false, "is_required": true, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 28732849, "category_dependent": true, "complex_is_collection": false}, {"id": 8790, "attribute_complex_id": 8788, "name": "Документ PDF", "description": "", "type": "URL", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": true}, {"id": 21841, "attribute_complex_id": 100001, "name": "Озон.Видео: ссылка", "description": "Укажите ссылку на видео (MP4, MOV). Продолжительность от 8 сек до 5 минут, размер файла не более 2ГБ", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": true}, {"id": 10096, "attribute_complex_id": 0, "name": "Цвет товара", "description": "Основной или доминирующий цвет товара. Если точного соответствия нет, используйте ближайшие цвета. Сложные цвета нужно описывать перечислением простых цветов. Например, если в товаре преобладают черный, желтый и белый цвета, то укажите их все простым перечислением. Атрибут \"Цвет товара\" — это основной цвет. Все остальные цвета можно перечислить в атрибуте \"Название цвета\", если он доступен в категории.", "type": "String", "is_collection": true, "is_required": false, "is_aspect": true, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 1494, "category_dependent": true, "complex_is_collection": false}, {"id": 4975, "attribute_complex_id": 0, "name": "Материал", "description": "Выберите из списка или укажите вручную. Можно добавить несколько значений через точку с запятой. Если точка с запятой есть в значении атрибута, поместите знак в кавычки \";\".", "type": "String", "is_collection": true, "is_required": false, "is_aspect": false, "max_value_count": 3, "group_name": "", "group_id": 0, "dictionary_id": 1503, "category_dependent": true, "complex_is_collection": false}, {"id": 8953, "attribute_complex_id": 0, "name": "Дополнительные функции", "description": "Выберите из списка или укажите вручную. Можно добавить несколько значений через точку с запятой. Если точка с запятой есть в значении атрибута, поместите знак в кавычки \";\".", "type": "String", "is_collection": true, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 1758, "category_dependent": true, "complex_is_collection": false}, {"id": 4082, "attribute_complex_id": 0, "name": "Размер упаковки (<PERSON><PERSON><PERSON><PERSON> х Ширина х Высота), см", "description": "Размеры сторон товара в упаковке в сантиметрах. Формат заполнения: ДхШх<PERSON>, где Д - длина, Ш - ш<PERSON><PERSON><PERSON><PERSON>, В - высота. Без единиц измерения. Пример: 7х6х1.5.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 22390, "attribute_complex_id": 0, "name": "Название группы", "description": "Введите одинаковое значение в этом атрибуте для объединения товаров в группу \"Похожие\". Обратите внимание, что товары в группе собираются в рамках одной категории второго уровня в личном кабинете. Если у товаров будет указано одно Название группы, но они находятся в разных категориях - вы получите несколько объединений товаров. Подробнее об объединении в Похожие можно прочитать здесь: https://seller-edu.ozon.ru/work-with-goods/zagruzka-tovarov/obedinenie-tovarov/pohojie-tovary", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 21845, "attribute_complex_id": 100002, "name": "Озон.Видеообложка: ссылка", "description": "Укажите ссылку на видео (MP4, MOV). Продолжительность от 8 до 30 секунд, размер файла не более 20МБ", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 8229, "attribute_complex_id": 0, "name": "Тип", "description": "Выберите из списка наиболее подходящий тип товара. Типы участвуют в распределении товаров по категориям на сайте Ozon. Чтобы правильно указать тип, найдите на сайте Ozon товары, похожие на ваш, и укажите в этом поле такой же тип.", "type": "String", "is_collection": false, "is_required": true, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 1960, "category_dependent": true, "complex_is_collection": false}, {"id": 7147, "attribute_complex_id": 0, "name": "Высота игрушки, см", "description": "Высота игрушки в естественном положении в сантиметрах без единиц измерения. Целое число или десятичная дробь (разделитель — точка)", "type": "Decimal", "is_collection": false, "is_required": true, "is_aspect": true, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 12553, "attribute_complex_id": 0, "name": "Перс<PERSON><PERSON><PERSON>", "description": "Выберите из списка или укажите вручную. Можно добавить несколько значений через точку с запятой. Если точка с запятой есть в значении атрибута, поместите знак в кавычки \";\".", "type": "String", "is_collection": true, "is_required": false, "is_aspect": false, "max_value_count": 12, "group_name": "", "group_id": 0, "dictionary_id": 85633300, "category_dependent": false, "complex_is_collection": false}, {"id": 22232, "attribute_complex_id": 0, "name": "ТН ВЭД коды ЕАЭС", "description": "Выберите одно значение из списка. Если вы не знаете, какой код нужно указать, то вам необходимо обратиться к таможенной службе или таможенному представителю.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 124412395, "category_dependent": true, "complex_is_collection": false}, {"id": 13214, "attribute_complex_id": 0, "name": "Минимальный возраст ребенка", "description": "Выберите из списка минимальный рекомендованный возраст ребенка. Если товар предназначен для любого возраста, то оставьте это поле пустым. Можно указать только одно значение.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 106850127, "category_dependent": false, "complex_is_collection": false}, {"id": 10097, "attribute_complex_id": 0, "name": "Название цвета", "description": "Словесное описание цвета товара. Если товар имеет красивое название цвета от поставщика, укажите его. Для косметики можно указать номера тонов и оттенков, и даже \"Нежная голубая лазурь\" будет уместна.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": true, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 21837, "attribute_complex_id": 100001, "name": "Озон.Видео: название", "description": "Укажите название для видео", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": true}, {"id": 4385, "attribute_complex_id": 0, "name": "Гарантийный срок", "description": "Период, в течение которого производитель отвечает за качество товара. А покупатель может оформить заявку на возврат по гарантийному случаю. Укажите срок в формате: количество лет, месяцев или дней. Например, 2 года или 3 месяца.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 9048, "attribute_complex_id": 0, "name": "Название модели (для объединения в одну карточку)", "description": "Заполните данное поле любым одинаковым значением на товарах, которые хотите объединить. И по разному, если товары нужно разъединить. Объединение произойдет при условии, что товары имеют одинаковый Тип и Бренд, и хотя бы одна вариативная характеристика заполнена по разному на всех товарах в объединении.", "type": "String", "is_collection": false, "is_required": true, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 23171, "attribute_complex_id": 0, "name": "#Хештеги", "description": "Хештеги работают так же, как в соцсетях: покупатели могут нажать на них в карточке или написать в поиске. Укажите тут тренд, стиль или тематику. Например, для одежды #oldmoney #kpop #бохо, для мебели #скандинавский_стиль #минимализм #лофт. Не добавляйте бренды, параметры или название товара.  Хештег должен начинаться со знака # и содержать только буквы и цифры. Если хештег состоит из 2+ слов, соедините их нижним подчеркиванием. Длина — не более 30 символов. Укажите не больше 30 хештегов, разделяйте пробелом", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 9447, "attribute_complex_id": 0, "name": "Вид детской фигурки", "description": "Выберите из списка. Можно указать только одно значение.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 5898289, "category_dependent": false, "complex_is_collection": false}, {"id": 13216, "attribute_complex_id": 0, "name": "Пол ребенка", "description": "Автоматически укажем значение \"Унисекс\", если оставите это поле пустым. Укажите пол ребенка, для которого рекомендован товар. Если нет явной гендерной принадлежности - выберите значение \"Унисекс\"\n", "type": "String", "is_collection": false, "is_required": true, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 106855177, "category_dependent": false, "complex_is_collection": false}, {"id": 4389, "attribute_complex_id": 0, "name": "Страна-изготовитель", "description": "Выберите из списка или укажите вручную. Можно добавить несколько значений через точку с запятой. Если точка с запятой есть в значении атрибута, поместите знак в кавычки \";\".", "type": "String", "is_collection": true, "is_required": false, "is_aspect": false, "max_value_count": 1, "group_name": "", "group_id": 0, "dictionary_id": 1935, "category_dependent": false, "complex_is_collection": false}, {"id": 4180, "attribute_complex_id": 0, "name": "Название", "description": "Название пишется по принципу: Тип + Бренд + Модель (серия + пояснение) + Артикул производителя + , (запятая) + Атрибут Название не пишется большими буквами (не используем caps lock). Перед атрибутом ставится запятая. Если атрибутов несколько, они так же разделяются запятыми. Если какой-то составной части названия нет - пропускаем её. Атрибутом может быть: цвет, вес, объём, количество штук в упаковке и т.д. Цвет пишется с маленькой буквы, в мужском роде, единственном числе. Слово цвет в названии не пишем. Точка в конце не ставится. Никаких знаков препинания, кроме запятой, не используем. Кавычки используем только для названий на русском языке. Примеры корректных названий: Смартфон Apple iPhone XS MT572RU/A, space black Кеды Dr. Martens Киноклассика, бело-черные, размер 43 Стиральный порошок Ariel Магия белого с мерной ложкой, 15 кг Соус Heinz Xtreme Tabasco суперострый, 10 мл Игрушка для животных Четыре лапы \"Бегающая мышка\" БММ, белый", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 22336, "attribute_complex_id": 0, "name": "Ключевые слова", "description": "Через точку с запятой укажите ключевые слова и словосочетания, которые описывают ваш товар. Используйте только соответствующие фактическому товару значения.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 7014, "attribute_complex_id": 0, "name": "Коллекционные", "description": "Выберите одно значение из выпадающего списка.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 1835, "category_dependent": true, "complex_is_collection": false}, {"id": 9070, "attribute_complex_id": 0, "name": "При<PERSON>на<PERSON> 18+", "description": "Признак для товаров, которые содержат эротику, сцены секса, изображения с нецензурными выражениями, даже если они написаны частично или со спец. символами, а также для товаров категории 18+ (только для взрослых).", "type": "Boolean", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 4497, "attribute_complex_id": 0, "name": "Вес с упаковкой, г", "description": "Можно указать только целое число или десятичную дробь. Разделитель — точка.", "type": "Decimal", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 8782, "attribute_complex_id": 0, "name": "Количество фигурок", "description": "Можно указать только целое число.", "type": "Integer", "is_collection": false, "is_required": false, "is_aspect": true, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 8789, "attribute_complex_id": 8788, "name": "Название файла PDF", "description": "", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": true}, {"id": 22273, "attribute_complex_id": 100001, "name": "Озон.Видео: товары на видео", "description": "Укажите товары, которые демонстрируются в вашем видео. Перечислите SKU через запятую (не более 5).", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": true}, {"id": 4191, "attribute_complex_id": 0, "name": "Аннотация", "description": "Описание товара, маркетинговый текст", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 11650, "attribute_complex_id": 0, "name": "Количество заводских упаковок", "description": "Сколько заводских упаковок продаёте под этим SKU. Пример: 24 банки детского пюре запаяны в 1 упаковку на заводе и в таком же виде продаются в других магазинах — укажите значение 1. \nДругой пример: завод выпускает лампочки в упаковках по 1 или 10 штук, а вы решили продавать по 3 лампочки и запаяли их в одну упаковку — значение атрибута будет 3", "type": "Integer", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 0, "category_dependent": false, "complex_is_collection": false}, {"id": 13215, "attribute_complex_id": 0, "name": "Максимальный возраст ребенка", "description": "Выберите из списка максимальный рекомендованный возраст ребенка. Если товар предназначен для любого возраста, выберите значение \"До 18 лет\". Можно указать только одно значение.", "type": "String", "is_collection": false, "is_required": false, "is_aspect": false, "max_value_count": 0, "group_name": "", "group_id": 0, "dictionary_id": 106849399, "category_dependent": false, "complex_is_collection": false}], "success": true, "error": null, "session_id": "session_1754036619"}