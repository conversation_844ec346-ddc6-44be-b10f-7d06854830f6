

# **构建基于Python的Ozon一键跟卖工具：从数据抓取到API集成的深度实践指南**

本报告旨在为开发一款自动化的Ozon平台商品跟卖工具提供一份详尽的技术实现方案。该工具的核心功能是：根据用户提供的Ozon商品页面URL，自动抓取（Scraping）该商品的核心信息，并利用Ozon Seller API将这些信息转化为符合规范的数据，最终在用户自己的店铺中实现一键上架（跟卖）。

我们将采用Python作为开发语言，并遵循专业的软件工程实践，构建一个模块化、可维护且具备高鲁棒性的应用程序。报告内容将覆盖从项目架构设计、环境配置，到核心的数据抓取策略、复杂的API调用链，直至最终的执行逻辑与高级运维考量，为开发者提供一条清晰、完整的实现路径。

## **第一部分：基础架构与项目设置**

一个成功的自动化工具始于一个清晰且稳固的架构。相较于编写一个庞大的单体脚本，我们将采用模块化的设计思路，将不同职责的功能进行解耦，这对于后续的调试、扩展和维护至关重要。

### **1.1 系统架构概览**

本工具的系统架构遵循关注点分离（Separation of Concerns）原则，主要由三个核心部分组成：

1. **Orchestrator (主控模块 main.py)**: 作为应用程序的入口和指挥中心，负责接收用户输入（商品URL），协调Scraper和API Client模块的工作，并处理它们之间的数据流转。  
2. **Scraper (抓取器模块 modules/scraper.py)**: 专职负责从给定的Ozon商品页面URL中提取数据。它将模拟浏览器行为，获取页面信息，并解析出关键的商品详情。  
3. **API Client (API客户端模块 modules/api\_client.py)**: 封装所有与Ozon Seller API的交互。它接收来自Scraper的原始数据，通过一系列链式API调用，完成数据转换、验证，并最终将商品信息提交至Ozon平台。

数据流向如下：用户提供URL \-\> main.py \-\> scraper.py (抓取数据) \-\> main.py (转换数据) \-\> api\_client.py (通过API链式调用上架商品) \-\> Ozon平台。

### **1.2 项目结构与依赖**

一个规范化的项目目录结构是保证代码清晰和团队协作效率的基础。建议采用以下结构：

ozon\_follower/  
├──.env                  \# 存储敏感配置信息，如API密钥和Cookie  
├──.gitignore            \# Git忽略文件，确保.env等文件不会被提交  
├── requirements.txt      \# 项目依赖库列表  
├── main.py               \# 主程序入口和业务流程编排  
├── config.py             \# 统一的配置加载模块  
├── modules/              \# 核心功能模块目录  
│   ├── \_\_init\_\_.py  
│   ├── scraper.py        \# Ozon商品信息抓取器类  
│   └── api\_client.py     \# Ozon Seller API客户端类  
└── api\_examples.txt      \# 用户提供的API示例（备查）

项目运行所需的核心Python库将在requirements.txt中定义。基于功能需求分析，至少需要以下库 1：

* python-dotenv: 用于从.env文件中加载环境变量，实现配置与代码的分离 4。  
* requests: 强大的HTTP客户端库，用于执行所有网络请求，包括网页抓取和API调用 6。  
* beautifulsoup4: 用于解析HTML文档，作为数据抓取的备用策略 3。

### **1.3 使用 python-dotenv 进行安全配置管理**

为了响应用户将敏感凭证（API密钥、Cookie）存放在.env文件的要求，我们必须建立一套安全的配置管理机制。这是一个至关重要的安全实践，可以有效防止密钥等敏感信息硬编码在代码中或意外泄露到版本控制系统。

首先，在项目根目录下创建.env文件，其内容结构如下 4：

代码段

\# Ozon Seller API Credentials  
OZON\_CLIENT\_ID="在此处填写您的Client-Id"  
OZON\_API\_KEY="在此处填写您的Api-Key"

\# Ozon Web Scraper Credentials  
\# 重要提示：请在登录Ozon后，从浏览器开发者工具中获取完整的Cookie字符串  
OZON\_SESSION\_COOKIE="在此处粘贴您的完整Cookie字符串"

**强烈建议**：立即将.env文件添加到.gitignore文件中，以防止其被提交到任何代码仓库 4。

代码段

\# Environment variables  
.env

\# Python artifacts  
\_\_pycache\_\_/  
\*.pyc

接着，创建config.py模块，该模块将负责加载并提供这些配置变量。这种方式将配置的读取逻辑集中化，使其他模块可以干净地导入和使用配置 5。

Python

\# config.py  
import os  
from dotenv import load\_dotenv

\# 从.env文件加载环境变量  
load\_dotenv()

\# Ozon API 配置  
OZON\_CLIENT\_ID \= os.getenv("OZON\_CLIENT\_ID")  
OZON\_API\_KEY \= os.getenv("OZON\_API\_KEY")

\# Ozon Scraper 配置  
OZON\_SESSION\_COOKIE \= os.getenv("OZON\_SESSION\_COOKIE")

\# API基础URL  
API\_BASE\_URL \= "https://api-seller.ozon.ru"

通过这种设计，整个应用程序的配置都得到了安全、集中的管理，为后续开发奠定了坚实的基础。

## **第二部分：Ozon商品抓取器模块 (scraper.py)**

成功跟卖的第一步是准确、稳定地获取源商品的所有信息。现代电商网站如Ozon广泛使用动态加载技术，这意味着传统的静态HTML解析方法非常脆弱，容易因网站前端的微小调整而失效。因此，本模块将采用一种更为先进和鲁棒的混合抓取策略。

### **2.1 主要抓取策略：拦截并模拟前端API请求**

最可靠的数据抓取方法并非解析用户最终看到的HTML，而是找到并模拟网站前端自身用来获取数据的API请求。这种方法直接命中数据源头，可以有效绕过HTML结构变化带来的不稳定性。

通过使用浏览器开发者工具（通常是F12打开，切换到“网络(Network)”面板），我们可以监视页面加载时发生的所有网络请求。在Ozon商品页面上，可以发现页面核心数据通常是通过一个XHR (XMLHttpRequest) 或 Fetch 请求加载的。一个典型的目标URL可能是 https://www.ozon.ru/api/composer-api.bx/page/json/v2 9。这个请求的响应体就是一个包含了页面全部信息的结构化JSON对象。

我们的OzonScraper类将使用requests库来精确复制这个API请求。至关重要的是，我们需要在请求头中包含从.env文件加载的OZON\_SESSION\_COOKIE。这个Cookie是用户登录状态的凭证，携带它进行请求，能让我们的脚本在Ozon服务器看来如同一个已登录的真实用户，从而大大降低被反爬虫系统识别和屏蔽的风险 10。

### **2.2 备用抓取策略：解析页面内嵌的JSON数据**

如果前端API接口发生变化或难以定位，我们需准备一个备用方案。许多动态网站会将渲染页面所需的所有数据预先打包成一个大的JSON对象，然后嵌入到HTML页面的一个\<script\>标签中。当页面加载时，JavaScript会读取这个内嵌的JSON并动态生成页面内容。

这个备用策略将使用BeautifulSoup库。具体步骤如下：

1. 使用requests获取完整的页面HTML内容。  
2. 使用BeautifulSoup解析HTML，并重点查找所有的\<script\>标签 11。  
3. 遍历这些标签，检查其内容。通常，目标JSON会存在于type为application/ld+json的标签中，或者是一个赋值给JavaScript变量的巨大字符串 13。  
4. 提取出这个JSON字符串，使用Python内置的json.loads()方法将其转换为Python字典 15。

这种方法虽然比直接模拟API要间接，但仍然远比依赖CSS选择器解析零散的HTML元素要稳定。

### **2.3 OzonScraper类的实现**

我们将逐步构建OzonScraper类，封装所有抓取逻辑。

Python

\# modules/scraper.py  
import requests  
import json  
from bs4 import BeautifulSoup

class OzonScraper:  
    def \_\_init\_\_(self, cookie: str):  
        self.session \= requests.Session()  
        self.session.headers.update({  
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",  
            "Cookie": cookie  
        })

    def scrape\_product\_data(self, url: str) \-\> dict | None:  
        """  
        主抓取方法，尝试主要策略，失败后回退到备用策略。  
        """  
        try:  
            \# 主要策略：直接请求内部JSON API  
            \# 注意：这里的API端点需要通过浏览器开发者工具确认  
            api\_url \= f"https://www.ozon.ru/api/composer-api.bx/page/json/v2?url={url.split('ozon.ru')\[-1\]}"  
            response \= self.session.get(api\_url, timeout=10)  
            response.raise\_for\_status()  
            data \= response.json()  
            return self.\_parse\_api\_json(data)  
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:  
            print(f"主抓取策略失败: {e}。尝试备用策略...")  
            \# 备用策略：抓取HTML并解析内嵌JSON  
            try:  
                response \= self.session.get(url, timeout=10)  
                response.raise\_for\_status()  
                return self.\_extract\_from\_script\_tags(response.text)  
            except Exception as fallback\_e:  
                print(f"备用抓取策略也失败了: {fallback\_e}")  
                return None

    def \_parse\_api\_json(self, data: dict) \-\> dict:  
        """解析从内部API获取的JSON数据。"""  
        \# 此处的解析逻辑高度依赖于Ozon返回的JSON结构  
        \# 需要仔细分析 \`data\` 变量，找到商品标题、价格、描述、图片、属性等信息  
        \# 这是一个示例性的路径，实际路径需要通过分析真实JSON来确定  
        widget\_states \= {k: json.loads(v) for k, v in data.get("widgetStates", {}).items()}  
          
        \# 寻找包含商品信息的核心widget  
        product\_info \= {}  
        for state in widget\_states.values():  
            if state.get("cellTrackingInfo", {}).get("product"):  
                p\_data \= state\["product"\]  
                product\_info\['name'\] \= p\_data.get("title")  
                product\_info\['price'\] \= p\_data.get("price")  
                product\_info\['final\_price'\] \= p\_data.get("finalPrice")  
                \#... 更多信息  
                break  
          
        \# 进一步解析其他widget以获取描述、图片、属性等  
        \#...  
        \# 返回一个标准化的字典  
        return product\_info

    def \_extract\_from\_script\_tags(self, page\_content: str) \-\> dict | None:  
        """解析HTML中内嵌的\<script\>标签里的JSON数据。"""  
        soup \= BeautifulSoup(page\_content, 'html.parser')  
        scripts \= soup.find\_all('script')  
        for script in scripts:  
            if script.string and 'window.\_\_APOLLO\_STATE\_\_' in script.string:  
                \# 这是一个常见的模式，数据存储在\`window.\_\_APOLLO\_STATE\_\_\`  
                json\_str \= script.string.split('window.\_\_APOLLO\_STATE\_\_ \= ').strip()  
                \# 去除末尾的分号  
                if json\_str.endswith(';'):  
                    json\_str \= json\_str\[:-1\]  
                data \= json.loads(json\_str)  
                \# 解析这个复杂的Apollo State JSON以提取所需信息  
                \#...  
                \# 返回一个标准化的字典  
                return self.\_parse\_apollo\_state(data)  
        return None

    def \_parse\_apollo\_state(self, apollo\_data: dict) \-\> dict:  
        """解析Apollo State JSON的辅助函数。"""  
        \# Apollo State的结构通常是规范化的，需要遍历查找  
        product\_info \= {}  
        for key, value in apollo\_data.get("ROOT\_QUERY", {}).items():  
            if isinstance(value, dict) and value.get("\_\_typename") \== "Product":  
                product\_info\['name'\] \= value.get("title")  
                \#... 递归或迭代查找所有需要的信息  
                break  
        return product\_info

### **2.4 鲁棒的错误处理**

网络请求是不可靠的。任何对外的HTTP调用都必须被包裹在健壮的错误处理逻辑中。我们的requests调用将使用try-except块来捕获常见的网络异常 7：

* requests.exceptions.ConnectionError: DNS解析失败、连接被拒绝等网络层问题。  
* requests.exceptions.Timeout: 请求在设定的时间内未收到响应。  
* requests.exceptions.HTTPError: 服务器返回了错误的HTTP状态码（如404 Not Found, 500 Internal Server Error）。通过调用response.raise\_for\_status()可以方便地触发此异常。

对于每种异常，程序都应记录详细的错误信息，并以一种可控的方式失败（例如返回None），以便主控模块main.py能够决定是重试还是终止操作。

## **第三部分：Ozon Seller API客户端模块 (api\_client.py)**

这个模块是连接我们抓取的数据和用户Ozon店铺的桥梁。它的核心任务是执行一个复杂且有序的API调用序列，以完成商品的创建。单纯地认为“跟卖”只是一个API调用是一个常见的误解；实际上，它是一个需要多个步骤、数据层层依赖的流程。

### **3.1 OzonApiClient类的设计**

我们将创建一个OzonApiClient类来封装所有API交互。

Python

\# modules/api\_client.py  
import requests  
import json

class OzonApiClient:  
    def \_\_init\_\_(self, client\_id: str, api\_key: str, base\_url: str):  
        self.client\_id \= client\_id  
        self.api\_key \= api\_key  
        self.base\_url \= base\_url  
        self.session \= requests.Session()  
        self.session.headers.update({  
            "Client-Id": self.client\_id,  
            "Api-Key": self.api\_key,  
            "Content-Type": "application/json"  
        })

    def \_post(self, endpoint: str, payload: dict) \-\> dict:  
        """通用的POST请求方法，包含错误处理。"""  
        url \= self.base\_url \+ endpoint  
        try:  
            response \= self.session.post(url, data=json.dumps(payload), timeout=15)  
            response.raise\_for\_status()  
            return response.json()  
        except requests.exceptions.HTTPError as e:  
            print(f"API请求HTTP错误: {e.response.status\_code} \- {e.response.text}")  
            raise  
        except requests.exceptions.RequestException as e:  
            print(f"API请求网络错误: {e}")  
            raise

    \# 后续将在此处实现API链式调用的各个方法

这种设计将认证头和会话管理集中在构造函数中，而\_post方法则统一了请求发送和基础错误处理的逻辑。

### **3.2 跟卖逻辑的核心：依赖驱动的API调用链**

成功在Ozon上创建一个新商品，需要严格遵循一个数据依赖流程。我们无法直接使用抓取到的文本信息（如“颜色：蓝色”）来调用创建接口，而必须先将这些信息转换为Ozon系统内部的ID。

第一步：获取类目树，确定category\_id  
抓取到的商品通常有面包屑导航，如“电子产品 \> 智能手机”。我们需要将这个路径映射到Ozon的category\_id。这需要调用POST /v1/description-category/tree接口。

Python

\# 在 OzonApiClient 类中  
def get\_category\_tree(self) \-\> list:  
    """获取完整的Ozon商品类目树。"""  
    payload \= {"language": "DEFAULT"}  
    response \= self.\_post("/v1/description-category/tree", payload)  
    return response.get("result",)

此方法返回一个嵌套的类目列表。我们的主程序需要递归遍历这个树，根据抓取到的类目名称找到对应的category\_id和type\_id。

第二步：获取类目属性，构建商品“骨架”  
在获得category\_id和type\_id后，我们必须知道这个类目下有哪些必填和可选的属性。这需要调用POST /v3/category/attribute接口。

Python

\# 在 OzonApiClient 类中  
def get\_category\_attributes(self, category\_id: int, type\_id: int) \-\> list:  
    """根据类目ID和类型ID，获取该类目下的所有属性定义。"""  
    payload \= {  
        "category\_id": \[category\_id\],  
        "type\_id": type\_id,  
        "language": "DEFAULT"  
    }  
    response \= self.\_post("/v3/category/attribute", payload)  
    return response.get("result",)

这个接口的响应至关重要，它返回一个属性列表，每个属性是一个JSON对象，包含了id（属性ID）、name（属性名，如“Бренд”）、is\_required（是否必填）以及一个关键字段dictionary\_id 17。这个响应定义了我们最终创建商品时必须遵循的“数据模板”。

第三步（条件性）：获取属性字典值  
分析上一步的响应时，如果某个属性的dictionary\_id大于0，这表示该属性的值不能是任意文本，而必须从一个预定义的“字典”（即下拉列表）中选择。例如，“颜色”属性。为了获取可选值及其对应的ID，我们需要调用POST /v2/category/attribute/values接口。

Python

\# 在 OzonApiClient 类中  
def get\_attribute\_values(self, category\_id: int, attribute\_id: int, last\_value\_id: int \= 0) \-\> list:  
    """获取特定属性的字典值列表。"""  
    payload \= {  
        "category\_id": category\_id,  
        "attribute\_id": attribute\_id,  
        "language": "DEFAULT",  
        "last\_value\_id": last\_value\_id,  
        "limit": 5000  
    }  
    response \= self.\_post("/v2/category/attribute/values", payload)  
    return response.get("result",)

主程序需要用抓取到的文本值（如“синий”）在这个返回的列表中进行匹配，从而找到正确的dictionary\_value\_id。

第四步：组装并创建商品  
当所有必需的ID都准备就绪后，我们终于可以调用POST /v2/product/import接口来创建商品了。

Python

\# 在 OzonApiClient 类中  
def create\_product(self, items: list) \-\> dict:  
    """  
    创建商品。items是经过完整映射和转换后的商品数据列表。  
    """  
    payload \= {"items": items}  
    return self.\_post("/v2/product/import", payload)

def check\_import\_status(self, task\_id: int) \-\> dict:  
    """根据任务ID检查商品创建状态。"""  
    payload \= {"task\_id": task\_id}  
    return self.\_post("/v1/product/import/info", payload)

create\_product的items参数是一个结构极其复杂的列表，其具体格式将在附录中详细说明。调用成功后会返回一个task\_id，我们可以用check\_import\_status方法来轮询创建结果。

### **3.3 实现交互式API助手**

为了满足用户对程序适应性的要求，即在API信息未知时进行提问，我们可以设计一个简单的交互式助手。

Python

\# 可以在 main.py 或一个独立的 utils.py 中实现  
def get\_api\_endpoint\_details(api\_name: str, known\_apis: dict) \-\> tuple\[str, str\]:  
    """如果API信息未知，则提示用户输入。"""  
    if api\_name not in known\_apis:  
        print(f"未找到API '{api\_name}' 的详细信息。")  
        method \= input(f"请输入 '{api\_name}' 的HTTP方法 (例如 POST): ").upper()  
        endpoint \= input(f"请输入 '{api\_name}' 的端点路径 (例如 /v1/description-category/tree): ")  
        known\_apis\[api\_name\] \= {"method": method, "endpoint": endpoint}  
        \# 可以考虑将更新后的 known\_apis 写回文件  
        return method, endpoint  
    details \= known\_apis\[api\_name\]  
    return details\["method"\], details\["endpoint"\]

在主逻辑中调用API前，可以先通过此函数获取端点信息，从而实现动态配置。

## **第四部分：核心应用逻辑与执行 (main.py)**

main.py是整个应用的粘合剂，它将scraper的输出转化为api\_client的输入，并编排整个跟卖流程。这个过程的核心是数据转换，其复杂性不容小觑。

### **4.1 主编排工作流**

main.py的执行逻辑将遵循以下步骤：

1. **初始化**: 导入并加载config.py中的配置。实例化OzonScraper和OzonApiClient对象。  
2. **用户输入**: 提示用户输入目标Ozon商品页面的URL。  
3. **数据抓取**: 调用scraper.scrape\_product\_data(url)，获取包含原始商品信息的字典。如果失败，则向用户报告并退出。  
4. API链式调用与数据映射:  
   a. 调用api\_client.get\_category\_tree()。  
   b. 根据抓取到的类目信息，在类目树中查找category\_id和type\_id。  
   c. 调用api\_client.get\_category\_attributes(category\_id, type\_id)获取属性模板。  
   d. 调用核心的map\_scraped\_to\_api\_payload函数（见下节），将抓取的数据映射为API所需的格式。  
5. **商品创建**: 调用api\_client.create\_product(mapped\_payload)提交创建请求。  
6. **结果确认**: 获取返回的task\_id，并调用api\_client.check\_import\_status(task\_id)来确认最终结果。  
7. **报告**:向用户清晰地展示每一步的结果，包括成功信息或详细的错误报告。

### **4.2 数据转换与映射管道**

这是整个应用中最具挑战性的部分。我们需要一个专门的函数来处理从无结构的抓取数据到高度结构化的API载荷的转换。

Python

\# main.py 中的核心映射函数  
def map\_scraped\_to\_api\_payload(scraped\_data: dict, api\_attributes: list, api\_client: OzonApiClient) \-\> list:  
    """  
    将抓取到的数据映射到Ozon API的创建商品载荷格式。  
    这是一个复杂的过程，需要处理属性匹配、字典值查找等。  
    """  
    item \= {  
        "offer\_id": f"SKU\_{scraped\_data.get('id', 'UNKNOWN')}",  \# 生成一个唯一的offer\_id  
        "name": scraped\_data.get("name"),  
        "price": str(scraped\_data.get("final\_price", 0)),  
        "vat": "0", \# 根据实际情况调整  
        "images": scraped\_data.get("image\_urls",),  
        "attributes":  
    }  
      
    \# 假设scraped\_data\['attributes'\]是一个字典，如 {'Бренд': 'Apple', 'Цвет': 'синий'}  
    scraped\_attrs \= scraped\_data.get('attributes', {})

    for attr\_def in api\_attributes:  
        attr\_id \= attr\_def\['id'\]  
        attr\_name \= attr\_def\['name'\]  
        is\_required \= attr\_def\['is\_required'\]  
        dictionary\_id \= attr\_def\['dictionary\_id'\]

        \# 尝试在抓取的数据中找到匹配的属性  
        if attr\_name in scraped\_attrs:  
            value \= scraped\_attrs\[attr\_name\]  
              
            api\_attr \= {"id": attr\_id, "complex\_id": 0, "values":}

            if dictionary\_id \> 0:  
                \# 需要从字典中查找值  
                \# 注意：这里需要调用API获取字典值  
                dict\_values \= api\_client.get\_attribute\_values(attr\_def\['category\_id'\], attr\_id)  
                found\_value \= next((v for v in dict\_values if v\['value'\] \== value), None)  
                if found\_value:  
                    api\_attr\['values'\].append({"dictionary\_value\_id": found\_value\['id'\]})  
                else:  
                    print(f"警告：在字典中未找到属性'{attr\_name}'的值'{value}'")  
            else:  
                \# 自由文本值  
                api\_attr\['values'\].append({"value": str(value)})  
              
            item\['attributes'\].append(api\_attr)  
        elif is\_required:  
            print(f"错误：缺少必填属性'{attr\_name}'的数据。")  
            \# 在此可以抛出异常或采取其他错误处理措施  
            return None

    return \[item\]

这个函数的健壮性直接决定了整个工具的成功率。它必须能够智能地处理名称匹配（例如，抓取到的“品牌”和API要求的“Бренд”之间的映射）、数据类型转换以及最关键的字典值查找。

### **4.3 完整代码与执行指南**

最终的main.py将整合上述所有逻辑。运行时，它会像一个向导一样，引导用户完成整个流程。

**执行步骤:**

1. 确保python和pip已安装。  
2. 在项目根目录运行 pip install \-r requirements.txt 来安装所有依赖。  
3. 根据\#1.3节的指导，填写.env文件中的OZON\_CLIENT\_ID, OZON\_API\_KEY, 和 OZON\_SESSION\_COOKIE。  
4. 在命令行中运行 python main.py。  
5. 根据提示，粘贴您想要跟卖的Ozon商品URL。  
6. 程序将开始执行，并打印每一步的进度信息。

## **第五部分：高级考量与最佳实践**

要将此工具从一个可运行的脚本提升为一个专业级的应用，必须考虑真实世界中的各种挑战。

* **5.1 规避检测与速率限制**:  
  * **反爬虫策略**: Ozon网站部署了反机器人系统 10。除了使用认证Cookie外，还应采取其他措施，如轮换  
    User-Agent请求头、在连续请求之间加入随机的、人性化的延迟、以及在需要大规模抓取时考虑使用代理服务器池来避免IP封锁 3。  
  * **API速率限制**: Ozon Seller API对请求频率有限制 18。我们的API客户端应该能够优雅地处理  
    HTTP 429 Too Many Requests错误。一个有效的策略是引入指数退避（Exponential Backoff）重试机制，例如使用backoff库 19，当遇到此错误时，程序会自动等待一段时间再重试，而不是立即失败。  
* 5.2 异步操作以提升可伸缩性:  
  当前同步的实现方式一次只能处理一个URL。如果业务需要批量处理上百个URL，同步执行将非常耗时。引入asyncio和aiohttp库进行异步改造，可以使程序并发地执行数十个网络请求（包括抓取和API调用），从而将总耗时从几小时缩短到几分钟。社区中已存在基于asyncio的Ozon API库 20，这证明了该方案的可行性和巨大优势。  
* 5.3 数据验证与清洗:  
  API调用的原则是“垃圾进，垃圾出”。在将抓取到的数据发送给Ozon API之前，必须进行严格的验证和清洗。这包括：检查必填字段是否存在，确认价格是有效的数字格式，移除描述中可能夹带的多余HTML标签或脚本，等等。这一步骤能显著减少API调用失败的次数，提高商品创建的成功率。  
* 5.4 长期维护策略:  
  Ozon的前端网站和后端API都会随着时间推移而更新。为了确保工具的长期可用性，应建立一套维护策略：  
  * **自动化测试**: 为scraper和api\_client的关键功能编写单元测试。当Ozon进行更新后，运行测试可以迅速定位到失效的部分。  
  * **监控官方文档**: 定期关注Ozon Seller API的官方文档 21，了解API的变更和废弃通知。  
  * **利用交互式助手**: 我们在\#3.3节设计的交互式API助手，为应对未在文档中明确说明的API端点变更提供了一个灵活的“热修复”机制。

## **结论**

本报告详细阐述了构建一个Ozon一键跟卖工具的全过程，从架构设计到具体实现，再到高级运维考量。其核心的复杂性并非在于单一的技术点，而在于**将两个完全异构的系统——Ozon的动态前端和其结构化的后端API——进行无缝对接**。

成功的关键在于以下几点：

1. **采用鲁棒的抓取策略**：优先模拟内部API，而非解析脆弱的HTML，并利用用户Cookie进行认证。  
2. **理解并实现API调用链**：认识到商品创建是一个多步骤、数据依赖的流程（获取类目 \-\> 获取属性 \-\> 获取字典值 \-\> 创建商品），并按顺序、有条件地执行。  
3. **重视数据转换**：整个应用的核心是map\_scraped\_to\_api\_payload函数，它负责将非结构化的抓取文本精确映射到API要求的ID和结构化数据上。  
4. **构建专业的应用**：通过模块化设计、安全配置、错误处理和考虑可伸缩性，将一个简单的想法转化为一个稳定、可维护的商业工具。

遵循本指南中提出的架构和方法，开发者将能够构建一个功能强大且高度自动化的Ozon跟卖解决方案。

## **附录：关键Ozon API数据结构参考**

为方便开发，此处整理了项目中最核心的API端点和数据结构。

### **表1：项目核心Ozon Seller API端点**

| 工作流中的目的 | HTTP 方法 | 端点路径 | 关键载荷参数 | 关键响应数据 |
| :---- | :---- | :---- | :---- | :---- |
| 获取类目树 | POST | /v1/description-category/tree | language | result (包含类目树的列表) |
| 获取类目属性 | POST | /v3/category/attribute | category\_id, type\_id | result (属性定义列表) |
| 获取属性字典值 | POST | /v2/category/attribute/values | category\_id, attribute\_id | result (字典值列表) |
| 创建/导入商品 | POST | /v2/product/import | items (商品数据列表) | task\_id |
| 检查导入状态 | POST | /v1/product/import/info | task\_id | result (任务状态和详情) |

### **表2：POST /v2/product/import 载荷（items内单项）注解**

以下是根据分析 24 整理的

items数组中单个商品对象的结构注解。

JSON

{  
  "attributes":  
    },  
    {  
      "complex\_id": 0,  
      "id": 85, // 属性ID，例如“品牌”  
      "values": \[  
        {  
          "dictionary\_value\_id": 0, // 如果是自由文本属性，此项为0  
          "value": "Apple" // 提供抓取到的文本值  
        }  
      \]  
    }  
    //... 其他所有必需和可选的属性  
  \],  
  "barcode": "UNIQUE\_BARCODE\_HERE", // 商品条码，如果需要  
  "category\_id": 17030010, // 商品类目ID，从类目树中查找得到  
  "color\_image": "",  
  "complex\_attributes":,  
  "depth": 10, // 深度 (mm)  
  "dimension\_unit": "mm",  
  "height": 150, // 高度 (mm)  
  "images":,  
  "name": "Смартфон Apple iPhone 15 256GB, синий", // 商品全名  
  "offer\_id": "YOUR\_UNIQUE\_SKU\_12345", // 您系统中的唯一商品编码  
  "old\_price": "99990", // 原价（划线价）  
  "price": "89990", // 当前售价  
  "vat": "0.20", // 增值税率，根据您的税务情况填写 "0", "0.10", 或 "0.20"  
  "weight": 171, // 重量 (g)  
  "weight\_unit": "g",  
  "width": 71 // 宽度 (mm)  
}

### **表3：POST /v3/category/attribute 响应（result内单项）注解**

以下是根据分析 17 整理的属性定义对象的结构注解。正确理解此结构是数据映射的关键。

| 属性键 (Attribute Key) | 数据类型 | 描述与在工作流中的重要性 |
| :---- | :---- | :---- |
| id | Integer | **核心**。这是属性的唯一标识符。在构建product/import的attributes列表时，必须使用这个ID。 |
| name | String | **匹配依据**。属性的人类可读名称（如“Бренд”）。我们的映射逻辑将使用这个名称与抓取到的属性名进行匹配。 |
| description | String | 属性的详细描述，有助于理解其含义。 |
| type | String | 属性值的类型，如String, Integer, URL。用于数据验证。 |
| is\_collection | Boolean | 指示该属性是否可以有多个值（例如，一个商品可以有多种“材质”）。 |
| is\_required | Boolean | **验证关键**。指示该属性是否为必填项。如果为true，我们的映射逻辑必须确保为其提供一个值。 |
| dictionary\_id | Integer | **流程分支点**。如果此值大于0，表示该属性的值必须从预定义列表中选择。这会触发对/v2/category/attribute/values接口的调用。如果为0，则可以接受自由文本输入。 |
| category\_dependent | Boolean | 指示该属性是否特定于当前类目。 |

#### **引用的著作**

1. A guide to web scraping in Python using Beautiful Soup \- Opensource.com, 访问时间为 七月 23, 2025， [https://opensource.com/article/21/9/web-scraping-python-beautiful-soup](https://opensource.com/article/21/9/web-scraping-python-beautiful-soup)  
2. Reference \- python-dotenv \- Saurabh Kumar, 访问时间为 七月 23, 2025， [https://saurabh-kumar.com/python-dotenv/reference/](https://saurabh-kumar.com/python-dotenv/reference/)  
3. BeautifulSoup Web Scraping: Step-By-Step Tutorial \- Bright Data, 访问时间为 七月 23, 2025， [https://brightdata.com/blog/how-tos/beautiful-soup-web-scraping](https://brightdata.com/blog/how-tos/beautiful-soup-web-scraping)  
4. How to Use Python Dotenv (python-dotenv) to Manage Environment Variables (2025), 访问时间为 七月 23, 2025， [https://www.youtube.com/watch?v=pyUyeepCOjE\&pp=0gcJCfwAo7VqN5tD](https://www.youtube.com/watch?v=pyUyeepCOjE&pp=0gcJCfwAo7VqN5tD)  
5. Using Python Environment Variables with Python Dotenv \- GeeksforGeeks, 访问时间为 七月 23, 2025， [https://www.geeksforgeeks.org/python/using-python-environment-variables-with-python-dotenv/](https://www.geeksforgeeks.org/python/using-python-environment-variables-with-python-dotenv/)  
6. Ultimate Guide to Web Scraping with Python Part 1: Requests and ..., 访问时间为 七月 23, 2025， [https://www.learndatasci.com/tutorials/ultimate-guide-web-scraping-w-python-requests-and-beautifulsoup/](https://www.learndatasci.com/tutorials/ultimate-guide-web-scraping-w-python-requests-and-beautifulsoup/)  
7. Quickstart — Requests 2.21.0 documentation \- Python requests, 访问时间为 七月 23, 2025， [https://3.python-requests.org/user/quickstart/](https://3.python-requests.org/user/quickstart/)  
8. BeautifulSoup Guide \- Scraping HTML Pages With Python \- ScrapeOps, 访问时间为 七月 23, 2025， [https://scrapeops.io/python-web-scraping-playbook/python-beautifulsoup-web-scraping/](https://scrapeops.io/python-web-scraping-playbook/python-beautifulsoup-web-scraping/)  
9. OZON parser \- Github-Gist, 访问时间为 七月 23, 2025， [https://gist.github.com/DxDiagDx/710ac65e117bdd45d4dbb3c64d07849c](https://gist.github.com/DxDiagDx/710ac65e117bdd45d4dbb3c64d07849c)  
10. sergerdn/ozon-search-queries-collector \- GitHub, 访问时间为 七月 23, 2025， [https://github.com/sergerdn/ozon-search-queries-collector](https://github.com/sergerdn/ozon-search-queries-collector)  
11. How to parse  
12. How do I extract scripts or stylesheets using Beautiful Soup? | WebScraping.AI, 访问时间为 七月 23, 2025， [https://webscraping.ai/faq/beautiful-soup/how-do-i-extract-scripts-or-stylesheets-using-beautiful-soup](https://webscraping.ai/faq/beautiful-soup/how-do-i-extract-scripts-or-stylesheets-using-beautiful-soup)  
13. Web Scraping Guide With Python Using Beautiful Soup \- PromptCloud, 访问时间为 七月 23, 2025， [https://www.promptcloud.com/blog/web-scraping-guide-with-python-using-beautiful-soup/](https://www.promptcloud.com/blog/web-scraping-guide-with-python-using-beautiful-soup/)  
14. oxylabs/Scraping-Dynamic-JavaScript-Ajax-Websites-With-BeautifulSoup \- GitHub, 访问时间为 七月 23, 2025， [https://github.com/oxylabs/Scraping-Dynamic-JavaScript-Ajax-Websites-With-BeautifulSoup](https://github.com/oxylabs/Scraping-Dynamic-JavaScript-Ajax-Websites-With-BeautifulSoup)  
15. Extract JSON from HTML using BeautifulSoup in Python \- GeeksforGeeks, 访问时间为 七月 23, 2025， [https://www.geeksforgeeks.org/python/extract-json-from-html-using-beautifulsoup-in-python/](https://www.geeksforgeeks.org/python/extract-json-from-html-using-beautifulsoup-in-python/)  
16. Quickstart — Requests 2.32.4 documentation, 访问时间为 七月 23, 2025， [https://requests.readthedocs.io/en/latest/user/quickstart/\#errors-and-exceptions](https://requests.readthedocs.io/en/latest/user/quickstart/#errors-and-exceptions)  
17. Get category attributes | Open Integrations Package, 访问时间为 七月 23, 2025， [https://en.openintegrations.dev/docs/Ozon/Attributes-and-features/Get-category-attributes/](https://en.openintegrations.dev/docs/Ozon/Attributes-and-features/Get-category-attributes/)  
18. Get products requests limits \- OpenIntegrations | Open Integrations Package, 访问时间为 七月 23, 2025， [https://en.openintegrations.dev/docs/Ozon/Attributes-and-features/Get-products-requests-limits/](https://en.openintegrations.dev/docs/Ozon/Attributes-and-features/Get-products-requests-limits/)  
19. Tutorial: Automate your SP-API Calls Using a Python SDK, 访问时间为 七月 23, 2025， [https://developer-docs.amazon.com/sp-api/docs/tutorial-automate-your-sp-api-calls-using-python-sdk](https://developer-docs.amazon.com/sp-api/docs/tutorial-automate-your-sp-api-calls-using-python-sdk)  
20. ozon-api \- PyPI, 访问时间为 七月 23, 2025， [https://pypi.org/project/ozon-api/](https://pypi.org/project/ozon-api/)  
21. API Documentation | Ozon Help, 访问时间为 七月 23, 2025， [https://docs.ozon.ru/global/en/api/](https://docs.ozon.ru/global/en/api/)  
22. Upload products via API | Ozon Help, 访问时间为 七月 23, 2025， [https://docs.ozon.ru/global/en/api/via-api/](https://docs.ozon.ru/global/en/api/via-api/)  
23. Main Page | Ozon Help, 访问时间为 七月 23, 2025， [https://docs.ozon.ru/global/en/](https://docs.ozon.ru/global/en/)  
24. Ozon.postman\_collection.json \- GitHub Gist, 访问时间为 七月 23, 2025， [https://gist.github.com/solaryssky/93dca51c3c715f8e0ef713f9545b8640](https://gist.github.com/solaryssky/93dca51c3c715f8e0ef713f9545b8640)  
25. Работа с Ozon Seller API в PHP \- Snipp.ru, 访问时间为 七月 23, 2025， [https://snipp.ru/php/ozon-seller-api](https://snipp.ru/php/ozon-seller-api)