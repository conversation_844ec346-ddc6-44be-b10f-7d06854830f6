#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试品牌映射功能的简化程序
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.scraper import OzonScraper
from modules.logger import main_logger
import config

def create_attribute_mapping() -> dict:
    """创建属性名称映射字典，用于提高匹配准确性"""
    return {
        # 品牌相关
        "бренд": ["brand", "brandname", "manufacturer", "производитель", "марка"],
        "brand": ["бренд", "производитель", "марка"],
    }

def find_matching_attribute_value(attr_name: str, scraped_attrs: dict, scraped_data: dict, attribute_mapping: dict) -> str:
    """查找匹配的属性值 - 简化版本，专门处理品牌"""
    attr_name_lower = attr_name.lower()
    
    # 对于品牌属性，始终返回"无品牌"
    if attr_name_lower in ["бренд", "brand"]:
        return "Без бренда"
    
    # 对于其他属性，返回默认值
    return "默认值"

def test_brand_mapping():
    """测试品牌映射功能"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 使用之前成功的类目信息
        description_category_id = 53968796  # Level 3 类目ID
        type_id = 971080300
        
        print(f"获取类目属性: description_category_id={description_category_id}, type_id={type_id}")
        
        # 获取类目属性
        api_attributes = api_client.get_category_attributes(description_category_id, type_id)
        
        if not api_attributes:
            print("未获取到属性列表")
            return
        
        print(f"获取到 {len(api_attributes)} 个属性")
        
        # 模拟抓取的数据
        scraped_data = {
            "name": "测试商品",
            "price": "100 ₽",
            "attributes": {}
        }
        
        # 创建属性映射
        attribute_mapping = create_attribute_mapping()
        
        # 测试品牌属性映射
        brand_attributes = []
        other_required_attributes = []
        
        for attr_def in api_attributes:
            attr_id = attr_def['id']
            attr_name = attr_def['name']
            is_required = attr_def.get('is_required', False)
            dictionary_id = attr_def.get('dictionary_id', 0)
            
            if is_required:
                if "бренд" in attr_name.lower() or "brand" in attr_name.lower():
                    brand_attributes.append(attr_def)
                else:
                    other_required_attributes.append(attr_def)
        
        print(f"\n找到 {len(brand_attributes)} 个品牌属性:")
        for attr in brand_attributes:
            print(f"  - {attr['name']} (ID: {attr['id']}, 必填: {attr.get('is_required', False)})")
        
        print(f"\n找到 {len(other_required_attributes)} 个其他必填属性:")
        for attr in other_required_attributes:
            print(f"  - {attr['name']} (ID: {attr['id']})")
        
        # 测试品牌属性的值匹配
        if brand_attributes:
            brand_attr = brand_attributes[0]
            attr_name = brand_attr['name']
            dictionary_id = brand_attr.get('dictionary_id', 0)
            
            print(f"\n测试品牌属性: {attr_name}")
            
            # 获取匹配的值
            matched_value = find_matching_attribute_value(
                attr_name, 
                scraped_data.get('attributes', {}), 
                scraped_data, 
                attribute_mapping
            )
            
            print(f"匹配的品牌值: '{matched_value}'")
            
            if dictionary_id > 0:
                print(f"需要在字典中查找值 (dictionary_id: {dictionary_id})")
                
                # 尝试获取字典值
                try:
                    print("获取品牌字典值...")
                    brand_values = api_client.get_attribute_values(
                        category_id=description_category_id,
                        attribute_id=brand_attr['id']
                    )
                    
                    if brand_values:
                        print(f"获取到 {len(brand_values)} 个品牌选项")
                        
                        # 查找"无品牌"选项
                        no_brand_found = False
                        for value in brand_values[:10]:  # 只检查前10个
                            value_text = value.get('value', '').lower()
                            if 'без' in value_text or 'бренд' in value_text:
                                print(f"找到可能的无品牌选项: '{value.get('value')}' (ID: {value.get('id')})")
                                no_brand_found = True
                                break
                        
                        if not no_brand_found:
                            print("前10个选项中未找到明确的无品牌选项")
                            print("前5个品牌选项:")
                            for i, value in enumerate(brand_values[:5], 1):
                                print(f"  {i}. {value.get('value')} (ID: {value.get('id')})")
                    else:
                        print("未获取到品牌字典值")
                        
                except Exception as e:
                    print(f"获取品牌字典值时出错: {e}")
                    print("这可能是API参数问题，但不影响使用固定的'无品牌'值")
            else:
                print("品牌属性支持自由文本输入")
        
        print(f"\n测试结论:")
        print(f"1. 品牌属性映射功能正常")
        print(f"2. 所有品牌都将映射为 'Без бренда'")
        print(f"3. 这避免了复杂的品牌匹配逻辑")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主程序"""
    print("=" * 60)
    print("品牌映射功能测试")
    print("=" * 60)
    
    success = test_brand_mapping()
    
    if success:
        print(f"\n测试完成! 品牌映射功能正常工作")
    else:
        print(f"\n测试失败! 请检查配置和网络连接")

if __name__ == "__main__":
    main()
