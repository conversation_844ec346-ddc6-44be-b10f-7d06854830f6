#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查品牌属性的字典值，找到"无品牌"选项
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.logger import api_logger
import config

def find_brand_attribute_and_values(api_client: OzonApiClient):
    """查找品牌属性并获取其字典值"""
    try:
        # 验证配置
        print("验证配置...")
        config.validate_config()
        
        # 测试API连接
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败，请检查您的API凭证")
            return
        
        print("API连接成功")
        
        # 使用之前成功的类目信息
        description_category_id = 53968796  # Level 3 类目ID
        type_id = 971080300
        
        print(f"获取类目属性: description_category_id={description_category_id}, type_id={type_id}")
        
        # 获取类目属性
        attributes = api_client.get_category_attributes(description_category_id, type_id)
        
        if not attributes:
            print("未获取到属性列表")
            return
        
        print(f"获取到 {len(attributes)} 个属性")
        
        # 查找品牌属性
        brand_attribute = None
        brand_keywords = ['бренд', 'brand', 'производитель', 'марка']
        
        for attr in attributes:
            attr_name = attr.get('name', '').lower()
            for keyword in brand_keywords:
                if keyword in attr_name:
                    brand_attribute = attr
                    print(f"找到品牌属性: {attr['name']} (ID: {attr['id']})")
                    break
            if brand_attribute:
                break
        
        if not brand_attribute:
            print("未找到品牌属性")
            return
        
        # 检查是否有字典值
        dictionary_id = brand_attribute.get('dictionary_id', 0)
        if dictionary_id <= 0:
            print("品牌属性不使用字典值（可以自由输入）")
            return
        
        print(f"品牌属性使用字典值，dictionary_id: {dictionary_id}")
        
        # 获取品牌属性的字典值
        print("获取品牌字典值...")
        brand_values = api_client.get_attribute_values(
            category_id=description_category_id,  # 注意这里使用 category_id
            attribute_id=brand_attribute['id']
        )
        
        if not brand_values:
            print("未获取到品牌字典值")
            return
        
        print(f"获取到 {len(brand_values)} 个品牌选项")
        
        # 查找"无品牌"选项
        no_brand_options = []
        no_brand_keywords = ['无品牌', 'без бренда', 'no brand', 'нет бренда', 'без марки']
        
        for value in brand_values:
            value_text = value.get('value', '').lower()
            value_id = value.get('id')
            
            for keyword in no_brand_keywords:
                if keyword.lower() in value_text:
                    no_brand_options.append({
                        'id': value_id,
                        'value': value.get('value'),
                        'keyword_matched': keyword
                    })
                    print(f"找到无品牌选项: '{value.get('value')}' (ID: {value_id})")
        
        if not no_brand_options:
            print("未找到明确的'无品牌'选项")
            print("前10个品牌选项:")
            for i, value in enumerate(brand_values[:10], 1):
                print(f"  {i}. {value.get('value')} (ID: {value.get('id')})")
        else:
            print(f"\n推荐使用的无品牌选项:")
            for option in no_brand_options:
                print(f"  ID: {option['id']}, 值: '{option['value']}'")
        
        # 保存结果到文件
        result_data = {
            'brand_attribute': brand_attribute,
            'no_brand_options': no_brand_options,
            'all_brand_values': brand_values[:50]  # 只保存前50个作为示例
        }
        
        with open('brand_values_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(result_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析结果已保存到: brand_values_analysis.json")
        
        return no_brand_options[0] if no_brand_options else None
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主程序"""
    try:
        # 初始化API客户端
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 查找品牌属性和无品牌选项
        no_brand_option = find_brand_attribute_and_values(api_client)
        
        if no_brand_option:
            print(f"\n成功找到无品牌选项!")
            print(f"ID: {no_brand_option['id']}")
            print(f"值: '{no_brand_option['value']}'")
        else:
            print(f"\n未找到无品牌选项，可能需要手动查找或使用自由文本")
        
        print(f"\n检查完成!")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
