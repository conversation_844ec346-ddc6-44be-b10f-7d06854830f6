#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查类目树结构的程序
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
import config

def inspect_category_tree(api_client: OzonApiClient):
    """检查类目树的结构"""
    try:
        print("获取类目树...")
        category_tree = api_client.get_category_tree()
        
        if not category_tree:
            print("获取类目树失败")
            return
        
        print(f"获取到 {len(category_tree)} 个顶级类目")
        
        # 保存完整的类目树到文件
        with open('category_tree_full.json', 'w', encoding='utf-8') as f:
            json.dump(category_tree, f, ensure_ascii=False, indent=2)
        print("完整类目树已保存到: category_tree_full.json")
        
        # 检查前几个类目的结构
        print("\n前3个类目的结构:")
        for i, category in enumerate(category_tree[:3], 1):
            print(f"\n类目 {i}:")
            for key, value in category.items():
                if key != 'children':  # 不显示子类目，太多了
                    print(f"  {key}: {value}")
            
            children = category.get('children', [])
            print(f"  children: {len(children)} 个子类目")
            
            if children:
                print(f"  第一个子类目的结构:")
                first_child = children[0]
                for key, value in first_child.items():
                    if key != 'children':
                        print(f"    {key}: {value}")
        
        # 查找包含我们目标 description_category_id 的类目
        target_id = 53968796
        print(f"\n查找 description_category_id={target_id} 的类目...")
        
        def find_category_by_desc_id(categories, target_id, path=""):
            for category in categories:
                current_path = f"{path} > {category.get('title', 'Unknown')}" if path else category.get('title', 'Unknown')
                
                if category.get('description_category_id') == target_id:
                    print(f"找到目标类目!")
                    print(f"路径: {current_path}")
                    print(f"完整信息:")
                    for key, value in category.items():
                        if key != 'children':
                            print(f"  {key}: {value}")
                    return category
                
                children = category.get('children', [])
                if children:
                    result = find_category_by_desc_id(children, target_id, current_path)
                    if result:
                        return result
            return None
        
        found_category = find_category_by_desc_id(category_tree, target_id)
        
        if not found_category:
            print(f"未找到 description_category_id={target_id} 的类目")
            
            # 查找相关的类目
            print("\n查找包含'пылесос'或'аксессуар'的类目...")
            
            def find_related_categories(categories, keywords, path=""):
                results = []
                for category in categories:
                    current_path = f"{path} > {category.get('title', 'Unknown')}" if path else category.get('title', 'Unknown')
                    title = category.get('title', '').lower()
                    
                    for keyword in keywords:
                        if keyword in title:
                            results.append({
                                'path': current_path,
                                'category': category
                            })
                            break
                    
                    children = category.get('children', [])
                    if children:
                        results.extend(find_related_categories(children, keywords, current_path))
                
                return results
            
            related = find_related_categories(category_tree, ['пылесос', 'аксессуар', 'vacuum'])
            
            if related:
                print(f"找到 {len(related)} 个相关类目:")
                for i, item in enumerate(related[:10], 1):
                    category = item['category']
                    print(f"\n{i}. {item['path']}")
                    print(f"   description_category_id: {category.get('description_category_id')}")
                    print(f"   type_id: {category.get('type_id')}")
                    
                    # 检查是否有其他可能的ID字段
                    for key, value in category.items():
                        if 'id' in key.lower() and key not in ['description_category_id', 'type_id']:
                            print(f"   {key}: {value}")
        
        # 尝试使用 description_category_id 作为 category_id
        print(f"\n尝试直接使用 description_category_id 作为 category_id...")
        try:
            attribute_values = api_client.get_attribute_values(
                category_id=target_id,  # 直接使用 description_category_id
                attribute_id=8229  # Тип 属性
            )
            
            if attribute_values:
                print(f"成功! 可以直接使用 description_category_id 作为 category_id")
                print(f"获取到 {len(attribute_values)} 个属性值")
                print("前5个值:")
                for i, value in enumerate(attribute_values[:5], 1):
                    print(f"  {i}. {value.get('value')} (ID: {value.get('id')})")
            else:
                print("失败: 返回空结果")
                
        except Exception as e:
            print(f"失败: {e}")
        
    except Exception as e:
        print(f"检查类目树失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 检查类目树
        inspect_category_tree(api_client)
        
        print(f"\n检查完成!")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
