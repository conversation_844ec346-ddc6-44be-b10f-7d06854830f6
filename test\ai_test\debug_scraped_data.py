#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试抓取数据的程序
检查抓取的数据结构，特别是品牌信息
"""

import sys
import os
import json
from typing import Dict, List, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.scraper import OzonScraper
from modules.logger import main_logger
import config

def analyze_scraped_data(scraped_data: Dict):
    """分析抓取的数据结构"""
    print("=" * 80)
    print("抓取数据分析")
    print("=" * 80)

    print(f"\n基本信息:")
    print(f"   商品名称: {scraped_data.get('name', 'N/A')}")
    print(f"   商品ID: {scraped_data.get('id', 'N/A')}")
    print(f"   商品价格: {scraped_data.get('price', scraped_data.get('final_price', 'N/A'))}")
    print(f"   商品URL: {scraped_data.get('url', 'N/A')}")

    # 检查品牌信息
    print(f"\n品牌信息检查:")
    brand_fields = ['brand', 'brandname', 'manufacturer', 'производитель', 'марка']
    brand_found = False

    for field in brand_fields:
        if field in scraped_data and scraped_data[field]:
            print(f"   [OK] 找到品牌字段 '{field}': {scraped_data[field]}")
            brand_found = True

    if not brand_found:
        print("   [X] 未找到直接的品牌字段")

        # 尝试从商品名称中提取品牌
        name = scraped_data.get('name', '')
        if name:
            print(f"   [?] 尝试从商品名称提取品牌: {name}")
            # 常见品牌列表（示例）
            common_brands = ['Apple', 'Samsung', 'Xiaomi', 'Huawei', 'Sony', 'LG', 'Philips', 'Bosch', 'Siemens']
            for brand in common_brands:
                if brand.lower() in name.lower():
                    print(f"   [!] 可能的品牌: {brand}")
    
    # 检查属性信息
    print(f"\n属性信息:")
    attributes = scraped_data.get('attributes', {})
    if attributes:
        print(f"   找到 {len(attributes)} 个属性:")
        for key, value in attributes.items():
            print(f"     - {key}: {value}")

        # 检查属性中是否有品牌相关信息
        print(f"\n在属性中查找品牌信息:")
        brand_keywords = ['бренд', 'brand', 'производитель', 'марка', 'manufacturer']
        for attr_key, attr_value in attributes.items():
            for keyword in brand_keywords:
                if keyword.lower() in attr_key.lower():
                    print(f"   [OK] 找到品牌属性 '{attr_key}': {attr_value}")
                    brand_found = True
    else:
        print("   [X] 未找到属性信息")

    # 检查图片信息
    print(f"\n图片信息:")
    images = scraped_data.get('image_urls', [])
    print(f"   图片数量: {len(images)}")
    if images:
        for i, img in enumerate(images[:3], 1):
            print(f"     {i}. {img}")
        if len(images) > 3:
            print(f"     ... 还有 {len(images) - 3} 张图片")

    # 检查其他可能包含品牌信息的字段
    print(f"\n其他字段检查:")
    other_fields = ['description', 'details', 'specifications', 'features']
    for field in other_fields:
        if field in scraped_data and scraped_data[field]:
            value = scraped_data[field]
            if isinstance(value, str) and len(value) > 100:
                value = value[:100] + "..."
            print(f"   {field}: {value}")
    
    return brand_found

def suggest_brand_extraction(scraped_data: Dict):
    """建议品牌提取方案"""
    print("\n" + "=" * 80)
    print("品牌提取建议")
    print("=" * 80)

    name = scraped_data.get('name', '')
    if name:
        print(f"商品名称: {name}")

        # 尝试提取品牌的几种方法
        print(f"\n可能的品牌提取方法:")

        # 方法1: 从名称开头提取
        words = name.split()
        if words:
            first_word = words[0]
            print(f"   1. 使用第一个词作为品牌: '{first_word}'")

        # 方法2: 查找常见品牌
        common_brands = [
            'Apple', 'Samsung', 'Xiaomi', 'Huawei', 'Sony', 'LG', 'Philips',
            'Bosch', 'Siemens', 'Panasonic', 'Canon', 'Nikon', 'HP', 'Dell',
            'Asus', 'Acer', 'Lenovo', 'Microsoft', 'Google', 'Amazon'
        ]

        found_brands = []
        for brand in common_brands:
            if brand.lower() in name.lower():
                found_brands.append(brand)

        if found_brands:
            print(f"   2. 在名称中找到的已知品牌: {', '.join(found_brands)}")

        # 方法3: 使用正则表达式
        import re
        # 查找大写字母开头的词（可能是品牌）
        brand_pattern = r'\b[A-Z][a-zA-Z]+\b'
        potential_brands = re.findall(brand_pattern, name)
        if potential_brands:
            print(f"   3. 可能的品牌词（大写开头）: {', '.join(potential_brands[:3])}")

def main():
    """主程序"""
    try:
        # 验证配置
        print("验证配置...")
        config.validate_config()

        # 初始化抓取器
        print("初始化网页抓取器...")
        scraper = OzonScraper(cookie=config.OZON_SESSION_COOKIE)

        # 使用之前测试的URL
        url = "https://www.ozon.ru/product/pylesos-thomas-aqua-pet-family-1355-vt-1921851629/"
        print(f"使用测试URL: {url}")

        print(f"开始抓取商品数据: {url}")

        # 抓取商品数据
        scraped_data = scraper.scrape_product_data(url)

        if not scraped_data:
            print("抓取商品数据失败")
            return

        print("商品数据抓取成功")

        # 保存原始数据到文件
        with open('scraped_data_debug.json', 'w', encoding='utf-8') as f:
            json.dump(scraped_data, f, ensure_ascii=False, indent=2)
        print("原始数据已保存到: scraped_data_debug.json")

        # 分析数据
        brand_found = analyze_scraped_data(scraped_data)

        # 如果没有找到品牌，提供建议
        if not brand_found:
            suggest_brand_extraction(scraped_data)

        print(f"\n分析完成!")

    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
