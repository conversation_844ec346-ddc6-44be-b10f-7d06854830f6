#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的抓取器
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.scraper import OzonScraper
from modules.logger import main_logger

def test_updated_scraper():
    """测试更新后的抓取器"""
    print("=" * 60)
    print("测试更新后的Ozon抓取器")
    print("=" * 60)
    
    # 测试URL
    test_url = "https://www.ozon.ru/product/nabor-stolovoy-posudy-shiping-iz-10-predm-kolichestvo-person-10-1727409461/?_bctx=CAYQmO81&at=BrtzxwvK9H0k8rG6TLq7j9qh0OXzGGiD9JpOgIvloq8j"
    
    # 使用一个简单的cookie（实际上会被内置的必需Cookie覆盖）
    cookie = "test_cookie=test_value"
    
    try:
        # 创建抓取器实例
        scraper = OzonScraper(cookie)
        
        print(f"开始抓取商品: {test_url}")
        print(f"使用的浏览器模拟选项: {scraper.best_impersonations}")
        print()
        
        # 执行抓取
        result = scraper.scrape_product_data(test_url)
        
        if result:
            print("✅ 抓取成功!")
            print(f"商品名称: {result.get('name', '未知')}")
            print(f"价格: {result.get('price', '未知')}")
            print(f"图片数量: {len(result.get('image_urls', []))}")
            print(f"数据字段数: {len(result.keys())}")
            
            # 显示所有可用字段
            print("\n可用数据字段:")
            for key, value in result.items():
                if isinstance(value, (str, int, float)):
                    display_value = str(value)[:100] + "..." if len(str(value)) > 100 else str(value)
                    print(f"  - {key}: {display_value}")
                elif isinstance(value, list):
                    print(f"  - {key}: 列表 ({len(value)} 项)")
                elif isinstance(value, dict):
                    print(f"  - {key}: 字典 ({len(value)} 键)")
                else:
                    print(f"  - {key}: {type(value).__name__}")
        else:
            print("❌ 抓取失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序"""
    test_updated_scraper()

if __name__ == "__main__":
    main()
