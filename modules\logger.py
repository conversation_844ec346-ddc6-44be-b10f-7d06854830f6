# modules/logger.py
"""
日志记录模块 - 提供统一的日志记录功能
"""

import logging
import os
from datetime import datetime
from typing import Optional

class OzonLogger:
    """OZON工具专用日志记录器"""
    
    def __init__(self, name: str = "ozon_tool", log_level: str = "INFO"):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 创建logs目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
        
        # 文件处理器 - 详细日志
        log_file = os.path.join(log_dir, f"ozon_tool_{datetime.now().strftime('%Y%m%d')}.log")
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 控制台处理器 - 简化输出
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 格式化器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter('%(levelname)s - %(message)s')
        
        file_handler.setFormatter(detailed_formatter)
        console_handler.setFormatter(simple_formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def debug(self, message: str, extra_data: dict = None):
        """记录调试信息"""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.debug(message)
    
    def info(self, message: str, extra_data: dict = None):
        """记录一般信息"""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.info(message)
    
    def warning(self, message: str, extra_data: dict = None):
        """记录警告信息"""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        self.logger.warning(message)
    
    def error(self, message: str, exception: Exception = None, extra_data: dict = None):
        """记录错误信息"""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        if exception:
            message = f"{message} | Exception: {str(exception)}"
        self.logger.error(message, exc_info=exception is not None)
    
    def critical(self, message: str, exception: Exception = None, extra_data: dict = None):
        """记录严重错误"""
        if extra_data:
            message = f"{message} | Data: {extra_data}"
        if exception:
            message = f"{message} | Exception: {str(exception)}"
        self.logger.critical(message, exc_info=exception is not None)

class APICallLogger:
    """API调用专用日志记录器"""
    
    def __init__(self, logger: OzonLogger):
        self.logger = logger
    
    def log_request(self, endpoint: str, payload: dict, attempt: int = 1):
        """记录API请求"""
        self.logger.info(f"API请求 [{attempt}] {endpoint}", {"payload": payload})
    
    def log_response(self, endpoint: str, status_code: int, response_data: dict, duration: float):
        """记录API响应"""
        self.logger.info(
            f"API响应 {endpoint} - {status_code} ({duration:.2f}s)",
            {"response": response_data}
        )
    
    def log_error(self, endpoint: str, error: Exception, attempt: int = 1):
        """记录API错误"""
        self.logger.error(
            f"API错误 [{attempt}] {endpoint}",
            exception=error
        )
    
    def log_retry(self, endpoint: str, attempt: int, max_attempts: int, delay: float):
        """记录重试信息"""
        self.logger.warning(
            f"API重试 {endpoint} - 尝试 {attempt}/{max_attempts}，等待 {delay}s"
        )

class ScrapingLogger:
    """抓取专用日志记录器"""
    
    def __init__(self, logger: OzonLogger):
        self.logger = logger
    
    def log_start(self, url: str):
        """记录抓取开始"""
        self.logger.info(f"开始抓取商品: {url}")
    
    def log_strategy_attempt(self, strategy: str, url: str):
        """记录抓取策略尝试"""
        self.logger.info(f"尝试抓取策略: {strategy} - {url}")
    
    def log_strategy_success(self, strategy: str, data_summary: dict):
        """记录抓取策略成功"""
        self.logger.info(f"抓取策略成功: {strategy}", data_summary)
    
    def log_strategy_failure(self, strategy: str, error: Exception):
        """记录抓取策略失败"""
        self.logger.warning(f"抓取策略失败: {strategy} - 错误: {str(error)}")
    
    def log_data_extracted(self, data_type: str, count: int):
        """记录数据提取结果"""
        self.logger.info(f"提取 {data_type}: {count} 项")
    
    def log_parsing_error(self, data_type: str, error: Exception):
        """记录解析错误"""
        self.logger.error(f"解析 {data_type} 时出错", exception=error)

class ProductLogger:
    """商品操作专用日志记录器"""
    
    def __init__(self, logger: OzonLogger):
        self.logger = logger
    
    def log_creation_start(self, product_name: str, category_id: int):
        """记录商品创建开始"""
        self.logger.info(f"开始创建商品: {product_name} (类目: {category_id})")
    
    def log_creation_success(self, product_id: int, task_id: int):
        """记录商品创建成功"""
        self.logger.info(f"商品创建成功: ID={product_id}, TaskID={task_id}")
    
    def log_creation_failure(self, errors: list, task_id: int = None):
        """记录商品创建失败"""
        self.logger.error(f"商品创建失败 (TaskID: {task_id})", extra_data={"errors": errors})
    
    def log_attribute_mapping(self, attr_name: str, matched_value: str, mapping_type: str):
        """记录属性映射"""
        self.logger.debug(f"属性映射: {attr_name} -> {matched_value} ({mapping_type})")
    
    def log_attribute_missing(self, attr_name: str, is_required: bool):
        """记录缺失属性"""
        level = "error" if is_required else "warning"
        message = f"缺失属性: {attr_name} ({'必填' if is_required else '可选'})"
        getattr(self.logger, level)(message)
    
    def log_price_update(self, product_id: int, old_price: float, new_price: float):
        """记录价格更新"""
        self.logger.info(f"价格更新: 商品{product_id} {old_price} -> {new_price}")
    
    def log_stock_update(self, product_id: int, old_stock: int, new_stock: int):
        """记录库存更新"""
        self.logger.info(f"库存更新: 商品{product_id} {old_stock} -> {new_stock}")

# 全局日志实例
main_logger = OzonLogger("ozon_main")
api_logger = APICallLogger(OzonLogger("ozon_api"))
scraping_logger = ScrapingLogger(OzonLogger("ozon_scraper"))
product_logger = ProductLogger(OzonLogger("ozon_product"))

def get_logger(name: str = "ozon_tool") -> OzonLogger:
    """获取日志记录器实例"""
    return OzonLogger(name)

def log_exception(func):
    """装饰器：自动记录函数异常"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger = get_logger(func.__module__)
            logger.error(f"函数 {func.__name__} 执行失败", exception=e)
            raise
    return wrapper

def log_performance(func):
    """装饰器：记录函数执行时间"""
    def wrapper(*args, **kwargs):
        import time
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger = get_logger(func.__module__)
            logger.debug(f"函数 {func.__name__} 执行完成，耗时 {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger = get_logger(func.__module__)
            logger.error(f"函数 {func.__name__} 执行失败，耗时 {duration:.2f}s", exception=e)
            raise
    return wrapper
