# OZON 一键跟卖工具

这是一个基于Python的自动化工具，可以从Ozon商品页面抓取商品信息，并通过Ozon Seller API自动上架到您的店铺中。

## 功能特点

### 核心功能
- 🕷️ **智能抓取**: 使用多重策略抓取Ozon商品信息，包括API拦截和HTML解析
- 🔄 **自动映射**: 智能将抓取的数据映射到Ozon API要求的格式
- 🛡️ **安全配置**: 使用.env文件管理敏感信息，避免硬编码
- 🔍 **类目匹配**: 自动识别商品类目并获取相应的属性要求
- ✅ **数据验证**: 创建前验证数据完整性，确保成功率
- 📊 **实时反馈**: 详细的进度显示和错误报告

### 新增功能 (v2.0)
- 📦 **商品管理**: 完整的商品生命周期管理（创建、更新、存档、删除）
- 💰 **价格管理**: 批量更新商品价格和原价
- 📊 **库存管理**: 实时更新商品库存数量
- 📈 **报告生成**: 商品表现和销售数据报告
- 🔧 **属性更新**: 批量更新商品属性和特征
- 📝 **日志记录**: 详细的操作日志和错误追踪
- 🎯 **增强匹配**: 改进的属性映射算法，支持更多商品类型

## 安装和配置

### 1. 环境要求

- Python 3.8+
- pip包管理器

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

1. 复制 `.env.example` 文件为 `.env`:
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入您的凭证:

```env
# Ozon Seller API Credentials
OZON_CLIENT_ID="您的Client-Id"
OZON_API_KEY="您的Api-Key"

# Ozon Web Scraper Credentials
OZON_SESSION_COOKIE="您的完整Cookie字符串"
```

### 4. 获取必要的凭证

#### Ozon Seller API 凭证

1. 登录 [Ozon Seller](https://seller.ozon.ru/)
2. 进入 "设置" → "API密钥"
3. 创建新的API密钥
4. 复制 `Client-Id` 和 `Api-Key`

#### Ozon Session Cookie

1. 在浏览器中登录 [Ozon](https://www.ozon.ru/)
2. 打开开发者工具 (F12)
3. 切换到 "Network" 标签
4. 刷新页面
5. 找到任意请求，复制完整的 `Cookie` 请求头内容

## 使用方法

### 基本使用

#### 一键跟卖功能

1. 运行主程序:
```bash
python main.py
```

2. 按提示输入要跟卖的Ozon商品URL:
```
https://www.ozon.ru/product/smartphone-apple-iphone-15-256gb-blue-123456789/
```

3. 程序将自动:
   - 抓取商品信息
   - 确定商品类目
   - 映射属性数据
   - 验证数据完整性
   - 创建商品

#### 商品管理功能

1. 运行商品管理工具:
```bash
python product_management_tool.py
```

2. 可用功能:
   - 查看商品列表
   - 查询商品状态
   - 更新商品价格
   - 更新商品库存
   - 存档/恢复商品
   - 删除商品
   - 生成商品报告

### 支持的URL格式

- `https://www.ozon.ru/product/product-name-123456789/`
- `https://www.ozon.ru/context/detail/id/123456789/`

## 项目结构

```
ozon_follower/
├── .env                        # 环境变量配置文件
├── .env.example               # 环境变量模板
├── .gitignore                 # Git忽略文件
├── requirements.txt           # Python依赖
├── main.py                   # 主程序入口（一键跟卖）
├── product_management_tool.py # 商品管理工具
├── config.py                 # 配置管理模块
├── modules/                  # 核心功能模块
│   ├── __init__.py
│   ├── scraper.py           # 商品信息抓取器（增强版）
│   ├── api_client.py        # Ozon API客户端（完整版）
│   ├── product_manager.py   # 商品管理器（新增）
│   └── logger.py            # 日志记录模块（新增）
├── logs/                    # 日志文件目录
├── test_system.py          # 系统测试脚本
├── setup_guide.md          # 快速设置指南
└── README.md               # 使用说明
```

## 工作流程

1. **初始化**: 验证配置和API连接
2. **抓取**: 从目标URL抓取商品信息
3. **类目匹配**: 确定商品所属类目
4. **属性映射**: 将抓取的数据映射到API格式
5. **数据验证**: 检查必填字段和属性
6. **商品创建**: 通过API创建商品
7. **状态监控**: 等待并报告创建结果

## 注意事项

### 安全性

- ⚠️ **永远不要**将 `.env` 文件提交到版本控制系统
- 🔐 定期更新您的API密钥和Cookie
- 🛡️ 在生产环境中使用更强的访问控制

### 使用限制

- 📊 遵守Ozon的API速率限制
- 🕐 避免过于频繁的请求
- 📝 确保遵守Ozon的服务条款

### 故障排除

#### 常见问题

1. **API连接失败**
   - 检查Client-Id和Api-Key是否正确
   - 确认API密钥是否已激活

2. **抓取失败**
   - 检查Cookie是否过期
   - 确认URL格式是否正确
   - 检查网络连接

3. **商品创建失败**
   - 检查必填属性是否完整
   - 确认类目选择是否正确
   - 查看详细错误信息

## 技术特性

### 抓取策略

1. **主要策略**: 拦截并模拟前端API请求
2. **备用策略**: 解析页面内嵌的JSON数据
3. **最后手段**: 从HTML元素直接提取

### 错误处理

- 🔄 自动重试机制
- ⏱️ 指数退避算法
- 📝 详细的错误日志

### 数据映射

- 🎯 智能属性匹配
- 📚 字典值查找
- ✅ 数据类型转换

## 开发和扩展

### 添加新的抓取策略

在 `modules/scraper.py` 中扩展 `OzonScraper` 类:

```python
def _try_new_strategy(self, url: str) -> dict | None:
    # 实现新的抓取逻辑
    pass
```

### 自定义属性映射

在 `main.py` 中修改 `map_scraped_to_api_payload` 函数:

```python
def custom_attribute_mapping(scraped_data: dict) -> dict:
    # 实现自定义映射逻辑
    pass
```

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规和平台服务条款。

## 支持

如有问题或建议，请查看项目文档或联系开发者。
