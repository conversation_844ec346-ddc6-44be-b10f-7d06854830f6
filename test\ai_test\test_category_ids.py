#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的 description_category_id_level_X 值作为 category_id 参数
用于确定哪个是正确的 category_id 值
"""

import sys
import os
from typing import Dict, List, Optional
import json
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.logger import api_logger
import config

def test_category_attribute_api(api_client: OzonApiClient, test_cases: List[Dict]) -> Dict:
    """
    测试不同的 category_id 值调用 /v1/description-category/attribute 接口
    
    Args:
        api_client: Ozon API 客户端
        test_cases: 测试用例列表，每个包含 category_id, type_id, description
    
    Returns:
        测试结果字典
    """
    results = {}
    
    print("=" * 80)
    print("🧪 开始测试不同的 category_id 值")
    print("=" * 80)
    
    for i, test_case in enumerate(test_cases, 1):
        category_id = test_case['category_id']
        type_id = test_case['type_id']
        description = test_case['description']
        
        print(f"\n📋 测试 {i}/{len(test_cases)}: {description}")
        print(f"   category_id: {category_id}")
        print(f"   type_id: {type_id}")
        print("-" * 60)
        
        try:
            # 调用 API
            start_time = time.time()
            attributes = api_client.get_category_attributes(
                category_id=category_id,
                type_id=type_id,
                language="DEFAULT"
            )
            duration = time.time() - start_time
            
            # 记录成功结果
            results[category_id] = {
                'status': 'success',
                'description': description,
                'type_id': type_id,
                'attributes_count': len(attributes),
                'duration': round(duration, 2),
                'sample_attributes': attributes[:3] if attributes else []  # 只保存前3个属性作为示例
            }
            
            print(f"✅ 成功! 获取到 {len(attributes)} 个属性")
            print(f"⏱️  耗时: {duration:.2f}s")
            
            # 显示前几个属性的基本信息
            if attributes:
                print("📝 前3个属性示例:")
                for j, attr in enumerate(attributes[:3], 1):
                    attr_name = attr.get('name', 'N/A')
                    attr_id = attr.get('id', 'N/A')
                    is_required = attr.get('is_required', False)
                    required_text = "必填" if is_required else "可选"
                    print(f"   {j}. {attr_name} (ID: {attr_id}) - {required_text}")
            
        except Exception as e:
            # 记录失败结果
            error_msg = str(e)
            results[category_id] = {
                'status': 'error',
                'description': description,
                'type_id': type_id,
                'error': error_msg,
                'duration': round(time.time() - start_time, 2)
            }
            
            print(f"❌ 失败! 错误: {error_msg}")
        
        # 添加延迟避免请求过快
        if i < len(test_cases):
            print("⏳ 等待 1 秒...")
            time.sleep(1)
    
    return results

def test_category_attribute_api_array_format(api_client: OzonApiClient, test_cases: List[Dict]) -> Dict:
    """
    测试数组格式的 category_id 值调用 /v1/description-category/attribute 接口
    某些 API 文档显示 category_id 应该是数组格式
    """
    results = {}

    print("=" * 80)
    print("🧪 开始测试数组格式的 category_id 值")
    print("=" * 80)

    for i, test_case in enumerate(test_cases, 1):
        category_id = test_case['category_id']  # 这里应该是数组
        type_id = test_case['type_id']
        description = test_case['description']

        print(f"\n📋 测试 {i}/{len(test_cases)}: {description}")
        print(f"   category_id: {category_id}")
        print(f"   type_id: {type_id}")
        print("-" * 60)

        try:
            # 直接调用 _post 方法，使用数组格式
            payload = {
                "category_id": category_id,  # 数组格式
                "type_id": type_id,
                "language": "DEFAULT"
            }

            start_time = time.time()
            response = api_client._post("/v1/description-category/attribute", payload)
            duration = time.time() - start_time

            attributes = response.get("result", [])

            # 记录成功结果
            key = str(category_id[0]) if isinstance(category_id, list) else str(category_id)
            results[key] = {
                'status': 'success',
                'description': description,
                'type_id': type_id,
                'attributes_count': len(attributes),
                'duration': round(duration, 2),
                'sample_attributes': attributes[:3] if attributes else []
            }

            print(f"✅ 成功! 获取到 {len(attributes)} 个属性")
            print(f"⏱️  耗时: {duration:.2f}s")

            # 显示前几个属性的基本信息
            if attributes:
                print("📝 前3个属性示例:")
                for j, attr in enumerate(attributes[:3], 1):
                    attr_name = attr.get('name', 'N/A')
                    attr_id = attr.get('id', 'N/A')
                    is_required = attr.get('is_required', False)
                    required_text = "必填" if is_required else "可选"
                    print(f"   {j}. {attr_name} (ID: {attr_id}) - {required_text}")

        except Exception as e:
            # 记录失败结果
            error_msg = str(e)
            key = str(category_id[0]) if isinstance(category_id, list) else str(category_id)
            results[key] = {
                'status': 'error',
                'description': description,
                'type_id': type_id,
                'error': error_msg,
                'duration': round(time.time() - start_time, 2)
            }

            print(f"❌ 失败! 错误: {error_msg}")

        # 添加延迟避免请求过快
        if i < len(test_cases):
            print("⏳ 等待 1 秒...")
            time.sleep(1)

    return results

def print_summary(results: Dict):
    """打印测试结果摘要"""
    print("\n" + "=" * 80)
    print("📊 测试结果摘要")
    print("=" * 80)
    
    successful_tests = []
    failed_tests = []
    
    for category_id, result in results.items():
        if result['status'] == 'success':
            successful_tests.append((category_id, result))
        else:
            failed_tests.append((category_id, result))
    
    print(f"\n✅ 成功的测试: {len(successful_tests)}")
    for category_id, result in successful_tests:
        print(f"   • category_id: {category_id} ({result['description']})")
        print(f"     属性数量: {result['attributes_count']}, 耗时: {result['duration']}s")
    
    print(f"\n❌ 失败的测试: {len(failed_tests)}")
    for category_id, result in failed_tests:
        print(f"   • category_id: {category_id} ({result['description']})")
        print(f"     错误: {result['error']}")
    
    # 推荐结果
    if successful_tests:
        print(f"\n🎯 推荐使用的 category_id:")
        # 按属性数量排序，选择属性最多的
        best_result = max(successful_tests, key=lambda x: x[1]['attributes_count'])
        category_id, result = best_result
        print(f"   category_id: {category_id}")
        print(f"   描述: {result['description']}")
        print(f"   属性数量: {result['attributes_count']}")
        print(f"   type_id: {result['type_id']}")
    else:
        print(f"\n⚠️  所有测试都失败了，请检查 type_id 或其他参数")

def save_results_to_file(results: Dict, filename: str = "category_test_results.json"):
    """将测试结果保存到文件"""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 测试结果已保存到: {filename}")
    except Exception as e:
        print(f"\n❌ 保存结果失败: {e}")

def main():
    """主程序"""
    try:
        # 验证配置
        print("🔧 验证配置...")
        config.validate_config()
        
        # 初始化API客户端
        print("🔧 初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        print("🔧 测试API连接...")
        if not api_client.test_api_connection():
            print("❌ API连接失败，请检查您的API凭证")
            return
        
        print("✅ API连接成功")
        
        # 定义测试用例
        # 根据您提供的日志信息，我们需要测试不同的组合

        # 从 Seller Tree API 响应中获取的所有可能值
        category_ids = [17027486, 53968796, 17040015]  # level_2, level_3, level_4
        type_id = 971080300  # 从日志中获取的 type_id

        test_cases = []

        # 首先测试原始组合
        for i, category_id in enumerate(category_ids, 2):
            test_cases.append({
                'category_id': category_id,
                'type_id': type_id,
                'description': f'Level {i} 类目 ({category_id}) + type_id {type_id}'
            })

        # 如果所有测试都失败，我们需要检查是否需要使用不同的 API 版本或参数格式
        # 让我们也测试一下是否需要将 category_id 作为数组传递（根据某些文档）
        print("🔍 首先测试标准格式...")
        results = test_category_attribute_api(api_client, test_cases)

        # 如果所有测试都失败，尝试其他格式
        if all(result['status'] == 'error' for result in results.values()):
            print("\n⚠️  所有标准格式测试都失败，尝试数组格式...")

            # 测试将 category_id 作为数组传递的格式
            array_test_cases = []
            for i, category_id in enumerate(category_ids, 2):
                array_test_cases.append({
                    'category_id': [category_id],  # 作为数组传递
                    'type_id': type_id,
                    'description': f'Level {i} 类目 ([{category_id}]) + type_id {type_id} (数组格式)'
                })

            array_results = test_category_attribute_api_array_format(api_client, array_test_cases)

            # 合并结果
            for key, value in array_results.items():
                results[f"array_{key}"] = value
        
        # 执行测试
        results = test_category_attribute_api(api_client, test_cases)
        
        # 打印摘要
        print_summary(results)
        
        # 保存结果
        save_results_to_file(results)
        
        print(f"\n🎉 测试完成!")
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
