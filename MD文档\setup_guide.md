# OZON 一键跟卖工具 - 快速设置指南

## 第一步：安装Python环境

确保您的系统已安装Python 3.8或更高版本：

```bash
python --version
```

如果没有安装，请从 [python.org](https://www.python.org/downloads/) 下载安装。

## 第二步：安装项目依赖

在项目目录中运行：

```bash
pip install -r requirements.txt
```

## 第三步：配置环境变量

### 1. 创建配置文件

复制示例配置文件：

```bash
cp .env.example .env
```

### 2. 获取Ozon Seller API凭证

1. 登录 [Ozon Seller后台](https://seller.ozon.ru/)
2. 导航到：设置 → API密钥
3. 点击"创建API密钥"
4. 复制生成的 `Client-Id` 和 `Api-Key`

### 3. 获取Ozon Session Cookie

1. 在浏览器中打开 [ozon.ru](https://www.ozon.ru/)
2. 登录您的账户
3. 按F12打开开发者工具
4. 切换到"Network"标签
5. 刷新页面
6. 点击任意请求
7. 在请求头中找到"Cookie"字段
8. 复制完整的Cookie值

### 4. 编辑.env文件

用文本编辑器打开`.env`文件，填入您的凭证：

```env
# Ozon Seller API Credentials
OZON_CLIENT_ID="您的Client-Id"
OZON_API_KEY="您的Api-Key"

# Ozon Web Scraper Credentials
OZON_SESSION_COOKIE="您的完整Cookie字符串"
```

## 第四步：测试系统

运行系统测试：

```bash
python test_system.py
```

如果所有测试都通过，您就可以开始使用了！

## 第五步：开始使用

运行主程序：

```bash
python main.py
```

按照提示输入要跟卖的Ozon商品URL即可。

## 常见问题

### Q: API连接失败怎么办？

A: 请检查：
- Client-Id和Api-Key是否正确
- API密钥是否已在Ozon后台激活
- 网络连接是否正常

### Q: 抓取失败怎么办？

A: 请检查：
- Cookie是否过期（重新获取）
- URL格式是否正确
- 是否能正常访问目标页面

### Q: 商品创建失败怎么办？

A: 请检查：
- 商品信息是否完整
- 类目选择是否正确
- 是否违反了Ozon的商品政策

## 安全提醒

⚠️ **重要**: 
- 不要将`.env`文件分享给他人
- 定期更新您的API密钥
- 不要在公共场所使用此工具

## 技术支持

如果遇到问题：
1. 首先运行 `python test_system.py` 诊断问题
2. 查看错误日志中的详细信息
3. 检查网络连接和凭证配置
