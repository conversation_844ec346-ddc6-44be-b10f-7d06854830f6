# config.py
import os
from dotenv import load_dotenv

# 从.env文件加载环境变量
load_dotenv()

# Ozon API 配置
OZON_CLIENT_ID = os.getenv("OZON_CLIENT_ID")
OZON_API_KEY = os.getenv("OZON_API_KEY")

# Ozon Scraper 配置
OZON_SESSION_COOKIE = os.getenv("OZON_SESSION_COOKIE")

# API基础URL
API_BASE_URL = "https://api-seller.ozon.ru"

# 验证必需的配置是否存在
def validate_config():
    """验证所有必需的配置是否已设置"""
    missing_configs = []
    
    if not OZON_CLIENT_ID:
        missing_configs.append("OZON_CLIENT_ID")
    if not OZON_API_KEY:
        missing_configs.append("OZON_API_KEY")
    if not OZON_SESSION_COOKIE:
        missing_configs.append("OZON_SESSION_COOKIE")
    
    if missing_configs:
        raise ValueError(f"缺少必需的配置项: {', '.join(missing_configs)}。请检查.env文件。")
    
    return True

# 默认请求头配置
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1"
}

# API请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3
RETRY_DELAY = 1  # 秒
