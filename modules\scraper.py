# modules/scraper.py
from curl_cffi import requests  # 使用 curl_cffi 替代标准 requests 以绕过反爬
import json
import time
import re
import random
import os
import warnings
from bs4 import BeautifulSoup
from urllib.parse import urlparse, parse_qs
from config import DEFAULT_HEADERS, REQUEST_TIMEOUT, MAX_RETRIES, RETRY_DELAY
from .logger import scraping_logger, log_exception, log_performance
from .cookie_manager import get_api_cookie

# 忽略 curl_cffi 的 __Secure- Cookie 警告
warnings.filterwarnings("ignore", message="`secure` changed to True for `__Secure-` prefixed cookies")

class OzonScraper:
    def __init__(self, cookie: str):
        self.full_cookie = cookie  # 保存完整的 Cookie

        # 基于测试结果的最佳浏览器模拟选项
        self.best_impersonations = ["chrome104", "chrome110", "chrome99", "edge99"]

        # 从.env文件或传入的cookie字符串中解析Cookie
        self.required_cookies = self._parse_cookies_from_env_or_string(cookie)

        # 推荐的请求头（基于测试结果）
        self.recommended_headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.3351.109',
            'sec-ch-ua-full-version-list': '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.169", "Microsoft Edge";v="138.0.3351.109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-o3-app-name': 'dweb_client',
            'x-o3-app-version': 'release_30-6-2025_551b2394',
            'x-o3-manifest-version': 'frontend-ozon-ru:551b23941a9d92fb35bd2c7850fae943cee48228,sf-render-api:100a986a9257dfdf22e7c029f43488da3e00e497',
            'x-o3-parent-requestid': '53f00ae1cd7aebb49bff904ecfb0b03d',
            'x-page-view-id': '56537c5d-1942-47eb-7dfc-a582686a7bb1'
        }

    def _parse_cookies_from_env_or_string(self, cookie_string: str) -> dict:
        """从环境变量或cookie字符串中解析Cookie"""
        cookies = {}

        # 首先尝试从环境变量中读取
        env_cookie = os.getenv("OZON_SESSION_COOKIE", "")
        if env_cookie:
            cookie_string = env_cookie
            scraping_logger.logger.info("使用.env文件中的Cookie")
        else:
            scraping_logger.logger.info("使用传入的Cookie字符串")

        if not cookie_string:
            scraping_logger.logger.warning("未找到Cookie，使用空Cookie")
            return {}

        try:
            # 分割Cookie字符串
            cookie_pairs = cookie_string.split(';')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.strip().split('=', 1)
                    cookies[key.strip()] = value.strip()

            scraping_logger.logger.info(f"成功解析了 {len(cookies)} 个Cookie")
            return cookies

        except Exception as e:
            scraping_logger.logger.error(f"解析Cookie字符串时出错: {e}")
            return {}

    @log_performance
    def scrape_product_data(self, url: str) -> dict | None:
        """
        主抓取方法，尝试主要策略，失败后回退到备用策略。
        """
        scraping_logger.log_start(url)

        # 首先尝试从URL中提取商品ID
        product_id = self._extract_product_id(url)
        if not product_id:
            scraping_logger.logger.error("无法从URL中提取商品ID")
            return None

        scraping_logger.logger.info(f"提取到商品ID: {product_id}")

        try:
            # 主要策略：直接请求内部JSON API
            scraping_logger.log_strategy_attempt("API策略", url)
            api_data = self._try_api_strategy(url, product_id)
            if api_data:
                # 确保返回的数据包含商品ID和URL
                api_data['id'] = product_id
                api_data['url'] = url
                scraping_logger.log_strategy_success("API策略", {
                    "name": api_data.get("name", "N/A"),
                    "attributes_count": len(api_data.get("attributes", {})),
                    "images_count": len(api_data.get("image_urls", []))
                })
                return api_data
        except Exception as e:
            scraping_logger.log_strategy_failure("API策略", e)

        try:
            # 备用策略：抓取HTML并解析内嵌JSON
            scraping_logger.log_strategy_attempt("HTML策略", url)
            html_data = self._try_html_strategy(url)
            if html_data:
                # 确保返回的数据包含商品ID和URL
                html_data['id'] = product_id
                html_data['url'] = url
                scraping_logger.log_strategy_success("HTML策略", {
                    "name": html_data.get("name", "N/A"),
                    "attributes_count": len(html_data.get("attributes", {})),
                    "images_count": len(html_data.get("image_urls", []))
                })
                return html_data
        except Exception as e:
            scraping_logger.log_strategy_failure("HTML策略", e)

        scraping_logger.logger.error("所有抓取策略都失败了")
        return None

    def _extract_product_id(self, url: str) -> str | None:
        """从URL中提取商品ID"""
        # Ozon商品URL通常格式为: https://www.ozon.ru/product/product-name-123456789/
        # 或者: https://www.ozon.ru/context/detail/id/123456789/
        
        # 方法1: 从路径中提取数字ID
        match = re.search(r'/product/[^/]+-(\d+)/?', url)
        if match:
            return match.group(1)
        
        # 方法2: 从detail/id路径中提取
        match = re.search(r'/detail/id/(\d+)/?', url)
        if match:
            return match.group(1)
        
        # 方法3: 从查询参数中提取
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        if 'id' in query_params:
            return query_params['id'][0]
        
        return None

    def _try_api_strategy(self, url: str, product_id: str) -> dict | None:
        """尝试通过API获取商品数据 - 使用测试验证的最佳实践"""
        # 添加随机延迟避免触发反爬
        time.sleep(random.uniform(1, 3))

        # 构建API URL（基于测试结果，使用成功的API端点）
        api_url = f"https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url={url}"

        # 随机选择最佳的浏览器模拟（基于测试结果）
        impersonation = random.choice(self.best_impersonations)

        scraping_logger.logger.info(f"使用 {impersonation} 模拟进行 API 请求")

        # 准备请求头（基于测试结果）
        api_headers = self.recommended_headers.copy()
        api_headers['referer'] = url

        for attempt in range(MAX_RETRIES):
            try:
                # 使用curl_cffi发送请求，应用测试验证的最佳配置
                response = requests.get(
                    api_url,
                    cookies=self.required_cookies,  # 使用测试验证的必需Cookie
                    headers=api_headers,
                    timeout=REQUEST_TIMEOUT,
                    impersonate=impersonation,  # 使用测试验证的最佳浏览器模拟
                    verify=False,  # 基于测试结果，跳过SSL验证
                    allow_redirects=True
                )
                response.raise_for_status()
                data = response.json()

                parsed_data = self._parse_api_json(data)
                if parsed_data:
                    return parsed_data

            except (Exception, json.JSONDecodeError, KeyError) as e:
                print(f"API策略第{attempt + 1}次尝试失败: {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (attempt + 1))

        return None

    def _try_html_strategy(self, url: str) -> dict | None:
        """尝试通过HTML解析获取商品数据 - 使用测试验证的最佳实践"""
        # 添加随机延迟
        time.sleep(random.uniform(2, 5))

        # 随机选择最佳的浏览器模拟（基于测试结果）
        impersonation = random.choice(self.best_impersonations)

        scraping_logger.logger.info(f"使用 {impersonation} 模拟进行 HTML 请求")

        # 准备HTML请求头（基于测试结果）
        html_headers = self.recommended_headers.copy()
        html_headers.update({
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "Cache-Control": "max-age=0",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Sec-Fetch-User": "?1",
            "Upgrade-Insecure-Requests": "1"
        })

        for attempt in range(MAX_RETRIES):
            try:
                # 使用curl_cffi发送请求，应用测试验证的最佳配置
                response = requests.get(
                    url,
                    cookies=self.required_cookies,  # 使用测试验证的必需Cookie
                    headers=html_headers,
                    timeout=REQUEST_TIMEOUT,
                    impersonate=impersonation,  # 使用测试验证的最佳浏览器模拟
                    verify=False,  # 基于测试结果，跳过SSL验证
                    allow_redirects=True
                )
                response.raise_for_status()
                return self._extract_from_script_tags(response.text)
                
            except Exception as e:
                print(f"HTML策略第{attempt + 1}次尝试失败: {e}")
                if attempt < MAX_RETRIES - 1:
                    time.sleep(RETRY_DELAY * (attempt + 1))
                    
        return None

    def _parse_api_json(self, data: dict) -> dict:
        """解析从内部API获取的JSON数据。"""
        try:
            # 解析widgetStates
            widget_states = {}
            if "widgetStates" in data:
                for key, value in data["widgetStates"].items():
                    try:
                        widget_states[key] = json.loads(value) if isinstance(value, str) else value
                    except json.JSONDecodeError:
                        continue

            product_info = {}

            # 从面包屑导航获取类目信息
            breadcrumbs_data = None
            for key, state in widget_states.items():
                if "breadCrumbs" in key and isinstance(state, dict) and "breadcrumbs" in state:
                    breadcrumbs_data = state["breadcrumbs"]
                    break

            # 从商品标题获取名称
            product_name = "未知"
            for key, state in widget_states.items():
                if "webProductHeading" in key and isinstance(state, dict) and "title" in state:
                    product_name = state["title"]
                    break

            # 从购物车组件获取价格信息
            price_info = None
            for key, state in widget_states.items():
                if "webAddToCart" in key and isinstance(state, dict) and "price" in state:
                    price_info = state["price"]
                    break

            # 从商品特征获取属性信息
            characteristics = {}
            for key, state in widget_states.items():
                if "webShortCharacteristics" in key and isinstance(state, dict) and "characteristics" in state:
                    for char in state["characteristics"]:
                        if "title" in char and "values" in char:
                            title_text = ""
                            if "textRs" in char["title"]:
                                title_text = "".join([item.get("content", "") for item in char["title"]["textRs"] if item.get("type") == "text"])

                            values = []
                            for value in char["values"]:
                                if "text" in value:
                                    values.append(value["text"])

                            if title_text and values:
                                characteristics[title_text] = ", ".join(values)
                    break

            # 从图片画廊获取图片信息
            images = []
            for key, state in widget_states.items():
                if "webGallery" in key and isinstance(state, dict) and "images" in state:
                    for img in state["images"]:
                        if "src" in img:
                            images.append(img["src"])
                    break

            product_info = {
                "name": product_name,
                "price": price_info,
                "image_urls": images,
                "attributes": characteristics,
                "breadcrumbs": breadcrumbs_data
            }

            return product_info if product_info else None
            
        except Exception as e:
            print(f"解析API JSON时出错: {e}")
            return None

    def _extract_product_basic_info(self, product_data: dict) -> dict:
        """提取商品基本信息"""
        info = {}

        # 商品名称
        name_fields = ["title", "name", "displayName", "productName"]
        for field in name_fields:
            if field in product_data and product_data[field]:
                info["name"] = product_data[field]
                break

        # 价格信息
        price_fields = ["price", "finalPrice", "currentPrice", "salePrice"]
        for field in price_fields:
            if field in product_data and product_data[field]:
                info["price"] = self._clean_price(product_data[field])
                break

        # 原价
        original_price_fields = ["originalPrice", "oldPrice", "listPrice"]
        for field in original_price_fields:
            if field in product_data and product_data[field]:
                info["original_price"] = self._clean_price(product_data[field])
                break

        # 商品ID
        id_fields = ["id", "productId", "itemId", "sku"]
        for field in id_fields:
            if field in product_data and product_data[field]:
                info["id"] = str(product_data[field])
                break

        # 描述
        desc_fields = ["description", "shortDescription", "summary"]
        for field in desc_fields:
            if field in product_data and product_data[field]:
                info["description"] = product_data[field]
                break

        # 品牌
        brand_fields = ["brand", "brandName", "manufacturer"]
        for field in brand_fields:
            if field in product_data and product_data[field]:
                info["brand"] = product_data[field]
                break

        # 评分和评论
        if "rating" in product_data:
            info["rating"] = product_data["rating"]
        if "reviewCount" in product_data:
            info["review_count"] = product_data["reviewCount"]

        # 库存状态
        if "inStock" in product_data:
            info["in_stock"] = product_data["inStock"]
        if "availability" in product_data:
            info["availability"] = product_data["availability"]

        return info

    def _clean_price(self, price_value) -> str:
        """清理价格数据，提取数字"""
        if isinstance(price_value, (int, float)):
            return str(price_value)

        if isinstance(price_value, str):
            # 移除货币符号和非数字字符，保留数字和小数点
            import re
            cleaned = re.sub(r'[^\d.,]', '', price_value)
            # 处理俄语数字格式（逗号作为小数点）
            if ',' in cleaned and '.' not in cleaned:
                cleaned = cleaned.replace(',', '.')
            elif ',' in cleaned and '.' in cleaned:
                # 如果同时有逗号和点，假设逗号是千位分隔符
                cleaned = cleaned.replace(',', '')
            return cleaned

        return str(price_value)

    def _extract_images(self, images_data) -> list:
        """提取图片URL列表"""
        image_urls = []
        
        if isinstance(images_data, list):
            for img in images_data:
                if isinstance(img, dict) and "src" in img:
                    image_urls.append(img["src"])
                elif isinstance(img, str):
                    image_urls.append(img)
        elif isinstance(images_data, dict) and "src" in images_data:
            image_urls.append(images_data["src"])
            
        return image_urls

    def _extract_attributes(self, characteristics_data) -> dict:
        """提取商品属性"""
        attributes = {}

        if isinstance(characteristics_data, list):
            for char in characteristics_data:
                if isinstance(char, dict):
                    # 支持多种属性格式
                    name_fields = ["name", "title", "key", "attribute", "property"]
                    value_fields = ["value", "val", "content", "text"]

                    name = None
                    value = None

                    # 查找属性名
                    for field in name_fields:
                        if field in char and char[field]:
                            name = char[field]
                            break

                    # 查找属性值
                    for field in value_fields:
                        if field in char and char[field]:
                            value = char[field]
                            break

                    if name and value:
                        attributes[name] = value

        elif isinstance(characteristics_data, dict):
            for key, value in characteristics_data.items():
                if isinstance(value, (str, int, float)):
                    attributes[key] = value
                elif isinstance(value, dict) and "value" in value:
                    attributes[key] = value["value"]
                elif isinstance(value, list) and value:
                    # 如果是列表，取第一个值或连接所有值
                    if len(value) == 1:
                        attributes[key] = value[0]
                    else:
                        attributes[key] = ", ".join(str(v) for v in value)

        return attributes

    def _extract_category_info(self, data: dict) -> dict:
        """提取类目信息"""
        category_info = {}

        # 查找面包屑导航
        breadcrumb_fields = ["breadcrumbs", "breadcrumb", "categoryPath", "navigation"]
        for field in breadcrumb_fields:
            if field in data and data[field]:
                breadcrumbs = data[field]
                if isinstance(breadcrumbs, list):
                    category_info["breadcrumbs"] = [item.get("name") or item.get("title") or str(item)
                                                  for item in breadcrumbs if item]
                    category_info["category_path"] = " > ".join(category_info["breadcrumbs"])
                break

        # 查找类目ID
        category_id_fields = ["categoryId", "category_id", "rubricId"]
        for field in category_id_fields:
            if field in data and data[field]:
                category_info["category_id"] = data[field]
                break

        # 查找类目名称
        category_name_fields = ["categoryName", "category", "rubric"]
        for field in category_name_fields:
            if field in data and data[field]:
                category_info["category_name"] = data[field]
                break

        return category_info

    def _extract_seller_info(self, data: dict) -> dict:
        """提取卖家信息"""
        seller_info = {}

        seller_fields = ["seller", "shop", "store", "vendor"]
        for field in seller_fields:
            if field in data and isinstance(data[field], dict):
                seller_data = data[field]

                # 卖家名称
                name_fields = ["name", "title", "shopName", "storeName"]
                for name_field in name_fields:
                    if name_field in seller_data:
                        seller_info["seller_name"] = seller_data[name_field]
                        break

                # 卖家ID
                id_fields = ["id", "sellerId", "shopId", "storeId"]
                for id_field in id_fields:
                    if id_field in seller_data:
                        seller_info["seller_id"] = seller_data[id_field]
                        break

                # 卖家评分
                if "rating" in seller_data:
                    seller_info["seller_rating"] = seller_data["rating"]

                break

        return seller_info

    def _extract_from_script_tags(self, page_content: str) -> dict | None:
        """解析HTML中内嵌的<script>标签里的JSON数据。"""
        soup = BeautifulSoup(page_content, 'html.parser')
        scripts = soup.find_all('script')

        for script in scripts:
            if script.string:
                # 查找Apollo State数据
                if 'window.__APOLLO_STATE__' in script.string:
                    try:
                        # 提取JSON字符串
                        json_str = script.string.split('window.__APOLLO_STATE__ = ')[1]
                        # 去除末尾的分号和其他JavaScript代码
                        json_str = json_str.split(';')[0].strip()
                        data = json.loads(json_str)
                        return self._parse_apollo_state(data)
                    except (json.JSONDecodeError, IndexError) as e:
                        print(f"解析Apollo State失败: {e}")
                        continue

                # 查找其他可能的数据结构
                if 'window.__INITIAL_STATE__' in script.string:
                    try:
                        json_str = script.string.split('window.__INITIAL_STATE__ = ')[1]
                        json_str = json_str.split(';')[0].strip()
                        data = json.loads(json_str)
                        return self._parse_initial_state(data)
                    except (json.JSONDecodeError, IndexError) as e:
                        print(f"解析Initial State失败: {e}")
                        continue

                # 查找JSON-LD结构化数据
                if script.get('type') == 'application/ld+json':
                    try:
                        data = json.loads(script.string)
                        return self._parse_json_ld(data)
                    except json.JSONDecodeError as e:
                        print(f"解析JSON-LD失败: {e}")
                        continue

        # 如果没有找到结构化数据，尝试从HTML中直接提取
        return self._extract_from_html_elements(soup)

    def _parse_apollo_state(self, apollo_data: dict) -> dict:
        """解析Apollo State JSON的辅助函数。"""
        product_info = {}

        try:
            # Apollo State通常包含规范化的数据
            root_query = apollo_data.get("ROOT_QUERY", {})

            for key, value in root_query.items():
                if isinstance(value, dict):
                    # 查找产品类型的对象
                    if value.get("__typename") == "Product":
                        product_info.update(self._extract_product_basic_info(value))

                    # 查找其他相关信息
                    if "title" in value or "name" in value:
                        product_info.update(self._extract_product_basic_info(value))

            # 查找其他可能的产品数据
            for key, value in apollo_data.items():
                if isinstance(value, dict) and ("Product:" in key or "product" in key.lower()):
                    product_info.update(self._extract_product_basic_info(value))

        except Exception as e:
            print(f"解析Apollo State时出错: {e}")

        return product_info if product_info else None

    def _parse_initial_state(self, initial_data: dict) -> dict:
        """解析Initial State数据"""
        product_info = {}

        try:
            # 递归查找产品信息
            def find_product_data(data, path=""):
                if isinstance(data, dict):
                    for key, value in data.items():
                        current_path = f"{path}.{key}" if path else key

                        if key.lower() in ['product', 'item', 'goods'] and isinstance(value, dict):
                            product_info.update(self._extract_product_basic_info(value))

                        if isinstance(value, (dict, list)):
                            find_product_data(value, current_path)
                elif isinstance(data, list):
                    for i, item in enumerate(data):
                        find_product_data(item, f"{path}[{i}]")

            find_product_data(initial_data)

        except Exception as e:
            print(f"解析Initial State时出错: {e}")

        return product_info if product_info else None

    def _parse_json_ld(self, json_ld_data) -> dict:
        """解析JSON-LD结构化数据"""
        product_info = {}

        try:
            if isinstance(json_ld_data, list):
                for item in json_ld_data:
                    if isinstance(item, dict) and item.get("@type") == "Product":
                        product_info = self._extract_json_ld_product(item)
                        break
            elif isinstance(json_ld_data, dict) and json_ld_data.get("@type") == "Product":
                product_info = self._extract_json_ld_product(json_ld_data)

        except Exception as e:
            print(f"解析JSON-LD时出错: {e}")

        return product_info if product_info else None

    def _extract_json_ld_product(self, product_data: dict) -> dict:
        """从JSON-LD产品数据中提取信息"""
        info = {}

        if "name" in product_data:
            info["name"] = product_data["name"]
        if "description" in product_data:
            info["description"] = product_data["description"]
        if "image" in product_data:
            images = product_data["image"]
            if isinstance(images, list):
                info["image_urls"] = images
            elif isinstance(images, str):
                info["image_urls"] = [images]

        # 提取价格信息
        if "offers" in product_data:
            offers = product_data["offers"]
            if isinstance(offers, dict):
                if "price" in offers:
                    info["price"] = offers["price"]
                if "priceCurrency" in offers:
                    info["currency"] = offers["priceCurrency"]
            elif isinstance(offers, list) and offers:
                offer = offers[0]
                if "price" in offer:
                    info["price"] = offer["price"]
                if "priceCurrency" in offer:
                    info["currency"] = offer["priceCurrency"]

        return info

    def _extract_from_html_elements(self, soup: BeautifulSoup) -> dict | None:
        """从HTML元素中直接提取商品信息（最后的备用方案）"""
        product_info = {}

        try:
            # 尝试提取标题
            title_selectors = [
                'h1[data-widget="webProductHeading"]',
                'h1.product-title',
                'h1',
                '[data-widget="webProductHeading"] h1'
            ]

            for selector in title_selectors:
                title_elem = soup.select_one(selector)
                if title_elem:
                    product_info["name"] = title_elem.get_text().strip()
                    break

            # 尝试提取价格
            price_selectors = [
                '[data-widget="webPrice"] span',
                '.price',
                '[data-testid="price-current"]'
            ]

            for selector in price_selectors:
                price_elem = soup.select_one(selector)
                if price_elem:
                    price_text = price_elem.get_text().strip()
                    # 提取数字
                    price_match = re.search(r'[\d\s]+', price_text.replace(',', '').replace(' ', ''))
                    if price_match:
                        product_info["price"] = price_match.group().strip()
                        break

            # 尝试提取图片
            img_selectors = [
                '[data-widget="webGallery"] img',
                '.product-images img',
                'img[data-testid="product-image"]'
            ]

            image_urls = []
            for selector in img_selectors:
                img_elems = soup.select(selector)
                for img in img_elems:
                    src = img.get('src') or img.get('data-src')
                    if src and src.startswith('http'):
                        image_urls.append(src)

            if image_urls:
                product_info["image_urls"] = image_urls

        except Exception as e:
            print(f"从HTML元素提取信息时出错: {e}")

        return product_info if product_info else None
