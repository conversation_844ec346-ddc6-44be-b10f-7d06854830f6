#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整流程的简化程序
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.scraper import OzonScraper
from modules.logger import main_logger
import config

# 导入主程序的函数
from main import create_attribute_mapping, find_matching_attribute_value, map_scraped_to_api_payload

def test_complete_flow():
    """测试完整的数据映射流程"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return False
        
        print("API连接成功")
        
        # 初始化抓取器
        print("初始化网页抓取器...")
        scraper = OzonScraper(cookie=config.OZON_SESSION_COOKIE)
        
        # 使用测试URL
        url = "https://www.ozon.ru/product/pylesos-thomas-aqua-pet-family-1355-vt-1921851629/"
        print(f"抓取商品数据: {url}")
        
        # 抓取商品数据
        scraped_data = scraper.scrape_product_data(url)
        
        if not scraped_data:
            print("抓取商品数据失败")
            return False
        
        print("商品数据抓取成功")
        print(f"  商品名称: {scraped_data.get('name', '未知')}")
        print(f"  商品价格: {scraped_data.get('price', '未知')}")
        print(f"  图片数量: {len(scraped_data.get('image_urls', []))}")
        print(f"  属性数量: {len(scraped_data.get('attributes', {}))}")
        
        # 使用已知的类目信息
        description_category_id = 53968796  # Level 3 类目ID
        type_id = 971080300
        
        print(f"获取类目属性: description_category_id={description_category_id}, type_id={type_id}")
        
        # 获取类目属性
        api_attributes = api_client.get_category_attributes(description_category_id, type_id)
        
        if not api_attributes:
            print("获取类目属性失败")
            return False
        
        required_count = sum(1 for attr in api_attributes if attr.get('is_required', False))
        print(f"获取到 {len(api_attributes)} 个属性，其中 {required_count} 个必填")
        
        # 映射商品数据
        print("映射商品数据...")
        mapped_items = map_scraped_to_api_payload(
            scraped_data, 
            description_category_id, 
            type_id, 
            api_attributes, 
            api_client
        )
        
        if not mapped_items:
            print("数据映射失败")
            return False
        
        print("数据映射成功!")
        print(f"映射的商品数量: {len(mapped_items)}")
        
        # 显示映射结果的摘要
        if mapped_items:
            item = mapped_items[0]
            print(f"商品信息:")
            print(f"  名称: {item.get('name', '未设置')}")
            print(f"  价格: {item.get('price', '未设置')}")
            print(f"  图片数量: {len(item.get('images', []))}")
            print(f"  属性数量: {len(item.get('attributes', []))}")
            
            # 显示品牌属性
            for attr in item.get('attributes', []):
                if attr.get('id') == 85:  # 品牌属性ID
                    values = attr.get('values', [])
                    if values:
                        brand_value = values[0].get('value', '未设置')
                        print(f"  品牌: {brand_value}")
                    break
        
        # 测试创建商品（可选）
        create_product = input("\n是否要创建商品到Ozon? (y/N): ").strip().lower()
        if create_product == 'y':
            print("创建商品...")
            result = api_client.create_product(mapped_items)
            
            if result.get("result"):
                task_id = result["result"].get("task_id")
                print(f"商品创建任务已提交，任务ID: {task_id}")
                print("请稍后在Ozon卖家中心查看商品创建状态")
            else:
                print("商品创建失败")
                print(f"错误信息: {result}")
        else:
            print("跳过商品创建")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主程序"""
    print("=" * 60)
    print("完整流程测试")
    print("=" * 60)
    
    success = test_complete_flow()
    
    if success:
        print(f"\n测试完成! 所有功能正常工作")
        print(f"品牌映射功能已修复，所有商品都将使用'无品牌'选项")
    else:
        print(f"\n测试失败! 请检查错误信息")

if __name__ == "__main__":
    main()
