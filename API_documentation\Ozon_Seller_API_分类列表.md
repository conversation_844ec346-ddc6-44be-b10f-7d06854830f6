# Ozon Seller API 分类接口列表

生成时间: 2025-07-23 21:41:15

## 商品上传和更新 (23 个)

- **[POST]** `/v1/description-category/tree` - 商品类别和类型的树形图
- **[POST]** `/v1/description-category/attribute` - 类别特征列表
- **[POST]** `/v1/description-category/attribute/values` - 特征值指南
- **[POST]** `/v1/description-category/attribute/values/search` - 根据属性的参考值进行搜索
- **[POST]** `/v3/product/import` - 创建或更新商品
- **[POST]** `/v1/product/import/info` - 查询商品添加或更新状态
- **[POST]** `/v1/product/import-by-sku` - 通过SKU创建商品
- **[POST]** `/v1/product/attributes/update` - 更新商品特征
- **[POST]** `/v1/product/pictures/import` - 上传或更新商品图片
- **[POST]** `/v3/product/list` - 品列表的
- **[POST]** `/v1/product/rating-by-sku` - 按SKU获得商品的内容排名
- **[POST]** `/v3/product/info/list` - 根据标识符获取商品信息
- **[POST]** `/v3/products/info/attributes` - 获取商品特征描述
- **[POST]** `/v4/product/info/attributes` - 获取商品特征描述
- **[POST]** `/v1/product/info/description` - 获取商品详细信息
- **[POST]** `/v4/product/info/limit` - 品类限制、商品的创建和更新
- **[POST]** `/v1/product/update/offer-id` - 从卖家的系统中改变商品货号
- **[POST]** `/v1/product/archive` - 将商品归档
- **[POST]** `/v1/product/unarchive` - 从档案中还原商品
- **[POST]** `/v2/products/delete` - 从存档删除没有SKU的商品
- **[POST]** `/v1/product/info/subscription` - 订阅该商品的用户数
- **[POST]** `/v1/product/related-sku/get` - 获取相关SKU
- **[POST]** `/v2/product/pictures/info` - 获取商品图片

## 商品条形码列表 (2 个)

- **[POST]** `/v1/barcode/add` - 为商品绑定条形码
- **[POST]** `/v1/barcode/generate` - 创建商品条形码

## 商品价格和库存 (7 个)

- **[POST]** `/v2/products/stocks` - 更新库存商品的数量
- **[POST]** `/v4/product/info/stocks` - 关于商品数量的信息
- **[POST]** `/v1/product/info/stocks-by-warehouse/fbs` - 关于卖家库存余额的信息
- **[POST]** `/v1/product/import/prices` - 更新价格
- **[POST]** `/v5/product/info/prices` - 获取商品价格信息
- **[POST]** `/v1/product/info/discounted` - 通过减价商品的SKU查找减价商品和主商品的信息
- **[POST]** `/v1/product/update/discount` - 为打折商品设置折扣

## 促销活动 (8 个)

- **[GET]** `/v1/actions` - 活动清单
- **[POST]** `/v1/actions/candidates` - 可用的促销商品清单
- **[POST]** `/v1/actions/products` - 参与 活动的商品列表
- **[POST]** `/v1/actions/products/activate` - 在促销活动中增加一个商品
- **[POST]** `/v1/actions/products/deactivate` - 从活动中删除商品
- **[POST]** `/v1/actions/discounts-task/list` - 申请折扣列表
- **[POST]** `/v1/actions/discounts-task/approve` - 同意折扣申请
- **[POST]** `/v1/actions/discounts-task/decline` - 取消折扣申请

## 定价策略 (12 个)

- **[POST]** `/v1/pricing-strategy/competitors/list` - 竞争对手名单
- **[POST]** `/v1/pricing-strategy/list` - 策略列表
- **[POST]** `/v1/pricing-strategy/create` - 创建策略
- **[POST]** `/v1/pricing-strategy/info` - 策略信息
- **[POST]** `/v1/pricing-strategy/update` - 更新策略
- **[POST]** `/v1/pricing-strategy/products/add` - 将商品添加到策略
- **[POST]** `/v1/pricing-strategy/strategy-ids-by-product-ids` - 策略ID列表
- **[POST]** `/v1/pricing-strategy/products/list` - 策略中的商品列表
- **[POST]** `/v1/pricing-strategy/product/info` - 竞争对手  的商品价格
- **[POST]** `/v1/pricing-strategy/products/delete` - 从策略中删除商品
- **[POST]** `/v1/pricing-strategy/status` - 更改策略状态
- **[POST]** `/v1/pricing-strategy/delete` - 删除策略

## 仓库 (2 个)

- **[POST]** `/v1/warehouse/list` - 仓库清单
- **[POST]** `/v1/delivery-method/list` - 仓库物流方式清单

## 多边形 (2 个)

- **[POST]** `/v1/polygon/create` - 创建一个快递的设施
- **[POST]** `/v1/polygon/bind` - 将快递方式与快递设施联系起来

## FBS/rFBS标志代码和营销管理 (7 个)

- **[POST]** `/v5/fbs/posting/product/exemplar/create-or-get` - 获取商品实例信息
- **[POST]** `/v4/fbs/posting/product/exemplar/validate` - 标志代码验证
- **[POST]** `/v4/fbs/posting/product/exemplar/set` - 检查并保存份数数据
- **[POST]** `/v5/fbs/posting/product/exemplar/set` - 检查并保存份数数据 (第5方案)
- **[POST]** `/v4/fbs/posting/product/exemplar/status` - 获取样件添加状态
- **[POST]** `/v4/posting/fbs/ship` - 搜集订单 (第4方案)
- **[POST]** `/v4/posting/fbs/ship/package` - 货件的部分装配 (第4方案)

## FBS配送 (2 个)

- **[POST]** `/v1/carriage/get` - 运输信息
- **[POST]** `/v1/posting/carriage-available/list` - 可供运输的列表

## rFBS配送 (6 个)

- **[POST]** `/v2/fbs/posting/delivering` - 将状态改成“运输中”
- **[POST]** `/v2/fbs/posting/tracking-number/set` - 添加跟踪号
- **[POST]** `/v2/fbs/posting/last-mile` - 状态改为“最后一英里”
- **[POST]** `/v2/fbs/posting/delivered` - 将状态改成“已送达”
- **[POST]** `/v2/fbs/posting/sent-by-seller` - 将状态改为“由卖家发送”
- **[POST]** `/v1/posting/cutoff/set` - 确认货件发运日期

## 通行证 (8 个)

- **[POST]** `/v1/pass/list` - 通行证列表
- **[POST]** `/v1/carriage/pass/create` - 创建通行证
- **[POST]** `/v1/carriage/pass/update` - 更新通行证
- **[POST]** `/v1/carriage/pass/delete` - 删除通行证
- **[POST]** `/v1/return/pass/create` - 创建退货通行证
- **[POST]** `/v1/return/pass/update` - 更新退货通行证
- **[POST]** `/v1/return/pass/delete` - 删除退货通行证
- **[POST]** `/v1/returns/company/fbs/info` - FBS退货数量

## FBO和rFBS商品集装 (13 个)

- **[POST]** `/v3/posting/fbs/unfulfilled/list` - 未处理货件列表 （第三 版）
- **[POST]** `/v3/posting/fbs/list` - 货件列表（第三版）
- **[POST]** `/v3/posting/fbs/get` - 按照ID获取货件信息
- **[POST]** `/v2/posting/fbs/get-by-barcode` - 按条形码获取有关货件的信息
- **[POST]** `/v2/posting/fbs/product/country/list` - 可用产地名单
- **[POST]** `/v2/posting/fbs/product/country/set` - 添加商品产地信息
- **[POST]** `/v2/posting/fbs/package-label` - 打印标签
- **[POST]** `/v2/posting/fbs/awaiting-delivery` - 货件装运
- **[POST]** `/v2/posting/fbs/cancel-reason/list` - 货件取消原因
- **[POST]** `/v1/posting/fbs/cancel-reason` - 货运取消原因
- **[POST]** `/v2/posting/fbs/cancel` - 取消货运
- **[POST]** `/v2/posting/fbs/product/change` - 为货件中的称重商品添加重量
- **[POST]** `/v2/posting/fbs/product/cancel` - 取消某些商品发货

## 退货 (1 个)

- **[POST]** `/v1/returns/list` - FBO和FBS退货信息

## 取消订单 (7 个)

- **[POST]** `/v1/conditional-cancellation/get` - 获取有关rFBS取消订单的消息
- **[POST]** `/v2/conditional-cancellation/list` - 获取 rFBS 取消申请列表
- **[POST]** `/v1/conditional-cancellation/list` - 获取rFBS取消申请列表
- **[POST]** `/v2/conditional-cancellation/approve` - 确认 rFBS 取消申请
- **[POST]** `/v1/conditional-cancellation/approve` - 确定rFBS取消订单
- **[POST]** `/v2/conditional-cancellation/reject` - 拒绝 rFBS 取消申请
- **[POST]** `/v1/conditional-cancellation/reject` - 拒绝取消rFBS申请

## rFBS商品集装 (8 个)

- **[POST]** `/v2/returns/rfbs/list` - 退货申请列表
- **[POST]** `/v2/returns/rfbs/get` - 退货申请信息
- **[POST]** `/v2/returns/rfbs/reject` - 拒绝退货申请
- **[POST]** `/v2/returns/rfbs/compensate` - 退还部分商品金额
- **[POST]** `/v2/returns/rfbs/verify` - 批准退货申请
- **[POST]** `/v2/returns/rfbs/receive-return` - 确认收到待检查商品
- **[POST]** `/v2/returns/rfbs/return-money` - 向买家退款
- **[POST]** `/v1/returns/rfbs/action/set` - 传递 rFBS  退货的可用操作

## 与买家的聊天 (7 个)

- **[POST]** `/v1/chat/send/message` - 发送信息
- **[POST]** `/v1/chat/send/file` - 发送文件
- **[POST]** `/v1/chat/start` - 创建新聊天
- **[POST]** `/v2/chat/list` - 聊天清单
- **[POST]** `/v2/chat/history` - 聊天历史记录
- **[POST]** `/v3/chat/history` - 聊天历史记录
- **[POST]** `/v2/chat/read` - 将信息标记为已读

## 报告 (7 个)

- **[POST]** `/v1/report/info` - 报告信息
- **[POST]** `/v1/report/list` - 报告清单
- **[POST]** `/v1/report/products/create` - 商品报告
- **[POST]** `/v1/report/postings/create` - 发货报告
- **[POST]** `/v1/finance/cash-flow-statement/list` - 财务报告
- **[POST]** `/v1/report/discounted/create` - 减价商品报告
- **[POST]** `/v1/report/warehouse/stock` - 关于FBS仓库库存报告

## 分析报告 (3 个)

- **[POST]** `/v1/analytics/data` - 分析数据
- **[POST]** `/v1/analytics/product-queries` - 获取商品搜索查询信息
- **[POST]** `/v1/analytics/product-queries/details` - 有关特定商品查询的信息

## 财务报告 (7 个)

- **[POST]** `/v2/finance/realization` - 商品销售报告 （第2版）
- **[POST]** `/v1/finance/realization/posting` - 按订单细分的商品销售报告
- **[POST]** `/v3/finance/transaction/list` - 交易清单
- **[POST]** `/v3/finance/transaction/totals` - 清单数目
- **[POST]** `/v1/finance/realization/by-day` - 每日商品销售报告
- **[POST]** `/v1/finance/compensation` - 赔偿报告
- **[POST]** `/v1/finance/decompensation` - 赔偿返还报告

## 测试 (10 个)

- **[POST]** `/v1/posting/fbs/split` - 将订单拆分为不带备货的货件
- **[POST]** `/v1/analytics/average-delivery-time` - 获取平均配送时间的分析数据
- **[POST]** `/v1/analytics/average-delivery-time/details` - 获取平均配送时间的详细分析
- **[POST]** `/v1/roles` - 使用API密钥获取角色和方式列表
- **[POST]** `/v6/fbs/posting/product/exemplar/set` - 检查并保存份数数据
- **[POST]** `/v6/fbs/posting/product/exemplar/create-or-get` - 获取已创建样件数据
- **[POST]** `/v5/fbs/posting/product/exemplar/status` - 获取样件添加状态
- **[POST]** `/v5/fbs/posting/product/exemplar/validate` - 标志代码验证
- **[POST]** `/v1/fbs/posting/product/exemplar/update` - Обновить данные экземпляров
- **[POST]** `/v1/product/info/wrong-volume` - 体积重量特征不正确的商品列表

## 评价 (7 个)

- **[POST]** `/v1/review/comment/create` - 对评价留下评论
- **[POST]** `/v1/review/comment/delete` - 删除对评价的评论
- **[POST]** `/v1/review/comment/list` - 评价的评论列表
- **[POST]** `/v1/review/change-status` - 更改评价状态
- **[POST]** `/v1/review/count` - 根据状态统计的评价数量
- **[POST]** `/v1/review/info` - 获取评价信息
- **[POST]** `/v1/review/list` - 获取评价列表

**总计: 149 个接口**