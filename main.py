# main.py
import sys
import re
import time
import json
import os
import warnings
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from modules.scraper import OzonScraper
from modules.api_client import OzonApiClient
from modules.product_manager import ProductManager
from modules.logger import main_logger, product_logger, log_exception
import config

# 忽略 curl_cffi 的 __Secure- Cookie 警告
warnings.filterwarnings("ignore", message="`secure` changed to True for `__Secure-` prefixed cookies")

# 请求记录目录
REQUESTS_DIR = "reslist"

def ensure_requests_directory():
    """确保请求记录目录存在"""
    if not os.path.exists(REQUESTS_DIR):
        os.makedirs(REQUESTS_DIR)
        print(f"📁 创建请求记录目录: {REQUESTS_DIR}")

def load_request_history():
    """加载请求历史记录统计"""
    ensure_requests_directory()

    try:
        # 统计现有的请求文件
        request_files = [f for f in os.listdir(REQUESTS_DIR) if f.startswith('request_') and f.endswith('.json')]
        print(f"📂 找到 {len(request_files)} 个历史请求记录文件")
        return len(request_files)
    except Exception as e:
        print(f"⚠️ 读取请求历史记录目录失败: {e}")
        return 0

def save_request_record(record: dict):
    """实时保存单个请求记录到独立文件"""
    ensure_requests_directory()

    try:
        # 生成唯一文件名：timestamp_requesttype_uuid.json
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        request_type = record.get('request_type', 'unknown').replace('/', '_').replace(' ', '_')
        unique_id = str(uuid.uuid4())[:8]
        filename = f"request_{timestamp}_{request_type}_{unique_id}.json"
        filepath = os.path.join(REQUESTS_DIR, filename)

        # 检查文件是否存在，如果存在则覆盖
        if os.path.exists(filepath):
            print(f"⚠️ 文件已存在，将覆盖: {filename}")

        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(record, f, ensure_ascii=False, indent=2)

        print(f"💾 已保存请求记录: {filename}")
        return filepath

    except Exception as e:
        print(f"⚠️ 保存请求记录失败: {e}")
        return None

def add_request_record(request_type: str, url: str = None, data: dict = None,
                      response: dict = None, success: bool = False, error: str = None,
                      save_full_response: bool = True):
    """添加请求记录并实时保存到文件

    Args:
        request_type: 请求类型
        url: 请求URL
        data: 请求数据
        response: 响应数据
        success: 是否成功
        error: 错误信息
        save_full_response: 是否保存完整响应数据（默认True）
    """

    # 处理响应数据
    response_data = response
    if response is not None and not save_full_response:
        # 如果明确指定不保存完整响应，则保持原样
        response_data = response

    record = {
        "timestamp": datetime.now().isoformat(),
        "request_type": request_type,
        "url": url,
        "request_data": data,
        "response_data": response_data,
        "success": success,
        "error": error,
        "session_id": f"session_{int(time.time())}"
    }

    # 实时保存到文件
    filepath = save_request_record(record)

    status = "成功" if success else "失败"
    print(f"📝 记录请求: {request_type} - {status}")

def load_all_request_records() -> List[dict]:
    """从文件系统加载所有请求记录"""
    ensure_requests_directory()
    records = []

    try:
        request_files = [f for f in os.listdir(REQUESTS_DIR) if f.startswith('request_') and f.endswith('.json')]

        for filename in request_files:
            filepath = os.path.join(REQUESTS_DIR, filename)
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    record = json.load(f)
                    records.append(record)
            except Exception as e:
                print(f"⚠️ 读取文件失败 {filename}: {e}")

    except Exception as e:
        print(f"⚠️ 读取请求记录目录失败: {e}")

    # 按时间戳排序
    records.sort(key=lambda x: x.get('timestamp', ''), reverse=True)
    return records

def search_request_history(keyword: str = None, request_type: str = None,
                          success_only: bool = False) -> List[dict]:
    """搜索请求历史记录"""
    results = load_all_request_records()

    if keyword:
        results = [r for r in results if keyword.lower() in str(r).lower()]

    if request_type:
        results = [r for r in results if r.get('request_type') == request_type]

    if success_only:
        results = [r for r in results if r.get('success', False)]

    return results

def print_request_summary():
    """打印请求记录摘要"""
    records = load_all_request_records()

    if not records:
        print("📊 暂无请求记录")
        return

    total = len(records)
    successful = len([r for r in records if r.get('success', False)])
    failed = total - successful

    # 统计请求类型
    type_counts = {}
    for record in records:
        req_type = record.get('request_type', 'unknown')
        type_counts[req_type] = type_counts.get(req_type, 0) + 1

    print("📊 请求记录摘要:")
    print(f"   总请求数: {total}")
    print(f"   成功: {successful} | 失败: {failed}")
    print(f"   成功率: {(successful/total*100):.1f}%" if total > 0 else "   成功率: 0%")
    print("   请求类型分布:")
    for req_type, count in type_counts.items():
        print(f"     - {req_type}: {count}")

def print_banner():
    """打印程序横幅"""
    print("=" * 60)
    print("           OZON 一键跟卖工具")
    print("    自动抓取商品信息并上架到您的店铺")
    print("=" * 60)
    print()

def clear_all_request_records():
    """清空所有请求记录文件"""
    ensure_requests_directory()

    try:
        request_files = [f for f in os.listdir(REQUESTS_DIR) if f.startswith('request_') and f.endswith('.json')]

        for filename in request_files:
            filepath = os.path.join(REQUESTS_DIR, filename)
            os.remove(filepath)

        print(f"✅ 已删除 {len(request_files)} 个请求记录文件")

    except Exception as e:
        print(f"⚠️ 清空请求记录失败: {e}")

def show_request_history_menu():
    """显示请求历史记录菜单"""
    while True:
        print("\n📊 请求历史记录管理")
        print("1. 查看所有记录")
        print("2. 查看成功的记录")
        print("3. 查看失败的记录")
        print("4. 按类型搜索")
        print("5. 按关键词搜索")
        print("6. 清空历史记录")
        print("7. 返回主程序")

        choice = input("请选择操作 (1-7): ").strip()

        if choice == "1":
            all_records = load_all_request_records()
            show_records(all_records)
        elif choice == "2":
            successful_records = search_request_history(success_only=True)
            show_records(successful_records, "成功的请求记录")
        elif choice == "3":
            failed_records = search_request_history(success_only=False)
            failed_records = [r for r in failed_records if not r.get('success', False)]
            show_records(failed_records, "失败的请求记录")
        elif choice == "4":
            print("\n可用的请求类型:")
            all_records = load_all_request_records()
            types = set(r.get('request_type', 'unknown') for r in all_records)
            for i, req_type in enumerate(sorted(types), 1):
                print(f"  {i}. {req_type}")

            type_choice = input("请输入请求类型: ").strip()
            if type_choice:
                filtered_records = search_request_history(request_type=type_choice)
                show_records(filtered_records, f"类型为 '{type_choice}' 的记录")
        elif choice == "5":
            keyword = input("请输入搜索关键词: ").strip()
            if keyword:
                filtered_records = search_request_history(keyword=keyword)
                show_records(filtered_records, f"包含 '{keyword}' 的记录")
        elif choice == "6":
            confirm = input("确认清空所有历史记录？(y/N): ").strip().lower()
            if confirm in ['y', 'yes']:
                clear_all_request_records()
        elif choice == "7":
            break
        else:
            print("无效选择，请重试")

def show_records(records: List[dict], title: str = "请求记录"):
    """显示请求记录"""
    if not records:
        print(f"\n📝 {title}: 无记录")
        return

    print(f"\n📝 {title} (共 {len(records)} 条):")
    print("-" * 80)

    for i, record in enumerate(records[-10:], 1):  # 只显示最近10条
        timestamp = record.get('timestamp', 'Unknown')
        req_type = record.get('request_type', 'unknown')
        success = record.get('success', False)
        status = "✅" if success else "❌"
        url = record.get('url', 'N/A')
        error = record.get('error', '')

        print(f"{i}. [{timestamp[:19]}] {status} {req_type}")
        print(f"   URL: {url}")
        if not success and error:
            print(f"   错误: {error}")
        print()

    if len(records) > 10:
        print(f"... 还有 {len(records) - 10} 条记录")

    input("按回车键继续...")

def validate_url(url: str) -> bool:
    """验证URL是否为有效的Ozon商品页面"""
    ozon_patterns = [
        r'https?://www\.ozon\.ru/product/[^/]+-\d+/?',
        r'https?://www\.ozon\.ru/context/detail/id/\d+/?'
    ]
    
    for pattern in ozon_patterns:
        if re.match(pattern, url):
            return True
    
    return False

def get_user_input() -> str:
    """获取用户输入的商品URL"""
    while True:
        print("请输入要跟卖的Ozon商品页面URL:")
        print("示例: https://www.ozon.ru/product/smartphone-apple-iphone-15-256gb-blue-123456789/")
        print("或者输入 'quit' 退出程序")
        print()
        
        url = input("URL: ").strip()
        
        if url.lower() == 'quit':
            print("程序退出。")
            sys.exit(0)
        
        if not url:
            print("URL不能为空，请重新输入。\n")
            continue

        if not validate_url(url):
            print("无效的Ozon商品URL格式，请检查后重新输入。\n")
            continue
        
        return url

def extract_category_path_from_scraped_data(scraped_data: dict) -> List[str]:
    """从抓取的数据中提取类目路径"""

    # 首先尝试从面包屑导航中获取
    if "breadcrumbs" in scraped_data and scraped_data["breadcrumbs"]:
        breadcrumbs = scraped_data["breadcrumbs"]
        if isinstance(breadcrumbs, list):
            # 提取每个面包屑的文本
            category_path = []
            for crumb in breadcrumbs:
                if isinstance(crumb, dict) and "text" in crumb:
                    category_path.append(crumb["text"])
                elif isinstance(crumb, str):
                    category_path.append(crumb)
            return category_path
        elif isinstance(breadcrumbs, str):
            return breadcrumbs.split(" > ")

    if "category_path" in scraped_data and scraped_data["category_path"]:
        return scraped_data["category_path"].split(" > ")

    # 如果没有面包屑，尝试从商品名称推断
    name = scraped_data.get("name", "").lower()

    # 电子产品类别
    if any(keyword in name for keyword in ["смартфон", "телефон", "iphone", "samsung", "xiaomi"]):
        return ["Электроника", "Смартфоны и телефоны"]
    elif any(keyword in name for keyword in ["ноутбук", "компьютер", "macbook", "laptop"]):
        return ["Электроника", "Компьютеры"]
    elif any(keyword in name for keyword in ["планшет", "ipad", "tablet"]):
        return ["Электроника", "Планшеты"]
    elif any(keyword in name for keyword in ["наушники", "airpods", "headphones"]):
        return ["Электроника", "Наушники и аудиотехника"]

    # одежда и аксессуары
    elif any(keyword in name for keyword in ["футболка", "рубашка", "свитер", "куртка"]):
        return ["Одежда, обувь и аксессуары", "Мужская одежда"]
    elif any(keyword in name for keyword in ["платье", "блузка", "юбка"]):
        return ["Одежда, обувь и аксессуары", "Женская одежда"]
    elif any(keyword in name for keyword in ["кроссовки", "ботинки", "туфли"]):
        return ["Одежда, обувь и аксессуары", "Обувь"]

    # дом и сад
    elif any(keyword in name for keyword in ["мебель", "стол", "стул", "диван"]):
        return ["Дом и сад", "Мебель"]
    elif any(keyword in name for keyword in ["посуда", "тарелка", "чашка"]):
        return ["Дом и сад", "Посуда"]

    # красота и здоровье
    elif any(keyword in name for keyword in ["крем", "шампунь", "косметика"]):
        return ["Красота и здоровье", "Косметика"]

    # спорт
    elif any(keyword in name for keyword in ["спорт", "фитнес", "тренажер"]):
        return ["Спорт и отдых", "Спортивные товары"]

    # книги
    elif any(keyword in name for keyword in ["книга", "роман", "учебник"]):
        return ["Книги", "Художественная литература"]

    # 默认类目
    else:
        return ["Товары для дома"]

def create_attribute_mapping() -> dict:
    """创建属性名称映射字典，用于提高匹配准确性"""
    return {
        # 品牌相关
        "бренд": ["brand", "brandname", "manufacturer", "производитель", "марка"],
        "brand": ["бренд", "производитель", "марка"],

        # 颜色相关
        "цвет": ["color", "colour", "цветовая гамма", "расцветка"],
        "color": ["цвет", "цветовая гамма", "расцветка"],

        # 尺寸相关
        "размер": ["size", "габариты", "dimensions"],
        "size": ["размер", "габариты"],

        # 材质相关
        "материал": ["material", "состав", "composition"],
        "material": ["материал", "состав"],

        # 重量相关
        "вес": ["weight", "масса"],
        "weight": ["вес", "масса"],

        # 型号相关
        "модель": ["model", "артикул", "sku"],
        "model": ["модель", "артикул"],

        # 技术规格
        "объем памяти": ["memory", "storage", "память", "накопитель"],
        "диагональ экрана": ["screen size", "display size", "размер экрана"],
        "операционная система": ["os", "operating system", "ос"],

        # 通用属性
        "тип": ["type", "вид", "категория"],
        "назначение": ["purpose", "применение", "использование"],
        "страна производства": ["country", "made in", "страна изготовления"],
    }

def find_matching_attribute_value(attr_name: str, scraped_attrs: dict, scraped_data: dict, attribute_mapping: dict) -> Optional[str]:
    """查找匹配的属性值"""
    attr_name_lower = attr_name.lower()

    # 1. 精确匹配
    if attr_name in scraped_attrs:
        return scraped_attrs[attr_name]

    # 2. 使用映射字典匹配
    if attr_name_lower in attribute_mapping:
        for synonym in attribute_mapping[attr_name_lower]:
            for scraped_key, scraped_value in scraped_attrs.items():
                if synonym.lower() in scraped_key.lower():
                    return scraped_value

    # 3. 模糊匹配
    for scraped_key, scraped_value in scraped_attrs.items():
        scraped_key_lower = scraped_key.lower()
        if attr_name_lower in scraped_key_lower or scraped_key_lower in attr_name_lower:
            return scraped_value

    # 4. 从商品基本信息中提取
    if attr_name_lower in ["бренд", "brand"]:
        # 返回字典中存在的通用品牌"Brand"
        return "Brand"
    elif attr_name_lower in ["название", "наименование", "name"]:
        return scraped_data.get("name")
    elif attr_name_lower in ["описание", "description"]:
        return scraped_data.get("description")
    elif "название модели" in attr_name_lower or "модель" in attr_name_lower:
        # 使用商品名称作为模型名称
        product_name = scraped_data.get("name", "")
        if product_name:
            return product_name
        else:
            return "Универсальная модель"
    elif "тип" in attr_name_lower:
        # 从抓取的数据中推断类型，返回字典中存在的值
        name = scraped_data.get("name", "").lower()
        if "robot" in name and "vacuum" in name:
            return "Аксессуар для робота-пылесоса"  # 这个值在字典中存在
        elif "робот" in name and "пылесос" in name:
            return "Аксессуар для робота-пылесоса"
        elif "пылесос" in name:
            return "Аксессуар для пылесоса"
        else:
            return "Аксессуар для пылесоса"  # 默认返回通用的吸尘器配件

    # 5. 特殊属性处理
    if "цвет" in attr_name_lower or "color" in attr_name_lower:
        # 尝试从商品名称中提取颜色
        name = scraped_data.get("name", "").lower()
        colors = ["черный", "белый", "красный", "синий", "зеленый", "желтый", "серый", "розовый", "фиолетовый"]
        for color in colors:
            if color in name:
                return color.capitalize()

    return None

def map_scraped_to_api_payload(scraped_data: dict, category_id: int, type_id: int,
                              api_attributes: List[dict], api_client: OzonApiClient) -> Optional[List[dict]]:
    """
    将抓取到的数据映射到Ozon API的创建商品载荷格式。
    这是一个复杂的过程，需要处理属性匹配、字典值查找等。
    """
    print("开始数据映射...")
    
    # 生成唯一的offer_id
    product_id = scraped_data.get("id", "UNKNOWN")
    offer_id = f"SKU_{product_id}_{int(time.time())}"
    
    # 构建基本商品信息
    item = {
        "offer_id": offer_id,
        "name": scraped_data.get("name", "未知商品"),
        "description_category_id": category_id,  # 修改为 description_category_id
        "type_id": type_id,  # 添加必需的 type_id 字段
        "price": str(scraped_data.get("price", scraped_data.get("final_price", "0"))),
        "vat": "0.0",   # 增值税率0%（根据国家要求）
        "images": scraped_data.get("image_urls", [])[:10],  # 最多10张图片
        "attributes": [],
        # 根据API文档要求添加必需的尺寸和重量信息
        "depth": 100,  # 深度，毫米
        "width": 100,   # 宽度，毫米
        "height": 50,   # 高度，毫米
        "dimension_unit": "mm",  # 尺寸单位
        "weight": 200,   # 重量，克
        "weight_unit": "g"  # 重量单位
    }

    # 处理价格格式
    price_str = item["price"]
    if price_str:
        # 清理价格字符串，只保留数字
        price_clean = re.sub(r'[^\d.]', '', str(price_str))
        if price_clean:
            item["price"] = price_clean

    # 添加原价（如果有）
    if scraped_data.get("original_price"):
        original_price = re.sub(r'[^\d.]', '', str(scraped_data["original_price"]))
        if original_price and float(original_price) > float(item["price"]):
            item["old_price"] = original_price

    # 添加商品描述
    if scraped_data.get("description"):
        item["description"] = scraped_data["description"][:5000]  # 限制描述长度

    # 添加商品尺寸信息（如果有）
    if scraped_data.get("dimensions"):
        dims = scraped_data["dimensions"]
        if isinstance(dims, dict):
            item.update({
                "height": dims.get("height", 0),
                "width": dims.get("width", 0),
                "depth": dims.get("depth", 0),
                "dimension_unit": dims.get("unit", "mm")
            })

    # 添加重量信息（如果有）
    if scraped_data.get("weight"):
        weight_str = str(scraped_data["weight"])
        weight_clean = re.sub(r'[^\d.]', '', weight_str)
        if weight_clean:
            item["weight"] = int(float(weight_clean))
            item["weight_unit"] = "g"
    
    # 处理商品属性
    scraped_attrs = scraped_data.get('attributes', {})
    print(f"找到 {len(scraped_attrs)} 个抓取的属性")

    # 创建属性映射字典，提高匹配准确性
    attribute_mapping = create_attribute_mapping()

    for attr_def in api_attributes:
        attr_id = attr_def['id']
        attr_name = attr_def['name']
        is_required = attr_def.get('is_required', False)
        dictionary_id = attr_def.get('dictionary_id', 0)

        print(f"处理属性: {attr_name} (ID: {attr_id}, 必填: {is_required})")

        # 尝试在抓取的数据中找到匹配的属性
        matched_value = find_matching_attribute_value(attr_name, scraped_attrs, scraped_data, attribute_mapping)
        
        if matched_value:
            api_attr = {"id": attr_id, "complex_id": 0, "values": []}
            
            if dictionary_id > 0:
                # 需要从字典中查找值
                print(f"  查找字典值: {matched_value}")
                try:
                    dict_values = api_client.get_attribute_values(category_id, attr_id, type_id)
                    dict_value_id = api_client.match_attribute_value(dict_values, str(matched_value))
                    
                    if dict_value_id:
                        api_attr['values'].append({"dictionary_value_id": dict_value_id})
                        print(f"  找到字典值ID: {dict_value_id}")
                    else:
                        print(f"  在字典中未找到值'{matched_value}'")
                        if is_required:
                            print(f"错误：缺少必填属性'{attr_name}'的有效值。")
                            return None
                except Exception as e:
                    print(f"  获取字典值时出错: {e}")
                    if is_required:
                        return None
            else:
                # 自由文本值
                api_attr['values'].append({"value": str(matched_value)})
                print(f"  使用文本值: {matched_value}")

            if api_attr['values']:
                item['attributes'].append(api_attr)

        elif is_required:
            print(f"错误：缺少必填属性'{attr_name}'的数据。")
            return None
        else:
            print(f"  跳过可选属性: {attr_name}")
    
    print(f"数据映射完成，生成了 {len(item['attributes'])} 个属性")
    return [item]

@log_exception
def main():
    """主程序入口"""
    print_banner()

    # 加载请求历史记录
    record_count = load_request_history()
    print_request_summary()
    print()

    # 询问用户是否要查看历史记录或继续主程序
    if record_count > 0:
        print("选择操作:")
        print("1. 继续主程序（抓取新商品）")
        print("2. 查看请求历史记录")

        choice = input("请选择 (1-2): ").strip()
        if choice == "2":
            show_request_history_menu()
            return

    try:
        # 验证配置
        main_logger.info("开始验证配置...")
        config.validate_config()
        main_logger.info("配置验证通过")

        # 初始化客户端
        print("🔧 初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        print("测试API连接...")
        connection_success = api_client.test_api_connection()
        add_request_record(
            request_type="api_connection_test",
            url=config.API_BASE_URL,
            data={"client_id": config.OZON_CLIENT_ID},
            success=connection_success,
            error=None if connection_success else "API连接失败"
        )

        if not connection_success:
            print("API连接失败，请检查您的API凭证")
            return

        # 初始化抓取器
        print("初始化网页抓取器...")
        scraper = OzonScraper(cookie=config.OZON_SESSION_COOKIE)

        # 初始化商品管理器
        print("初始化商品管理器...")
        product_manager = ProductManager(api_client)
        print("初始化完成")
        print()
        
        # 获取用户输入
        url = get_user_input()
        print(f"开始处理商品: {url}")
        print()

        # 抓取商品数据
        print("抓取商品数据...")
        scraped_data = scraper.scrape_product_data(url)

        # 记录抓取请求
        add_request_record(
            request_type="product_scraping",
            url=url,
            data={"scraping_method": "ozon_web_scraper"},
            response=scraped_data if scraped_data else None,
            success=bool(scraped_data),
            error=None if scraped_data else "抓取商品数据失败"
        )

        if not scraped_data:
            print("抓取商品数据失败，请检查URL或网络连接")
            return

        print("商品数据抓取成功")
        print(f"   商品名称: {scraped_data.get('name', '未知')}")
        print(f"   商品价格: {scraped_data.get('price', scraped_data.get('final_price', '未知'))}")
        print(f"   图片数量: {len(scraped_data.get('image_urls', []))}")
        print(f"   属性数量: {len(scraped_data.get('attributes', {}))}")
        print()

        # 获取类目树
        print("获取商品类目树...")
        category_tree = api_client.get_category_tree()

        # 记录类目树获取请求（保存完整响应数据）
        add_request_record(
            request_type="get_category_tree",
            url=f"{config.API_BASE_URL}/v1/description-category/tree",
            data={"language": "DEFAULT"},
            response=category_tree,  # 保存完整的类目树数据
            success=bool(category_tree),
            error=None if category_tree else "获取类目树失败"
        )

        if not category_tree:
            print("获取类目树失败")
            return
        print("类目树获取成功")

        # 确定商品类目
        print("确定商品类目...")

        # 首先尝试通过 Seller Tree API 获取准确的类目信息
        category_result = api_client.get_category_info_from_scraped_data(scraped_data)

        # 获取完整的API响应数据用于记录
        sku = api_client.extract_sku_from_scraped_data(scraped_data)
        full_category_response = None
        if sku:
            full_category_response = api_client.get_category_by_sku(sku)

        # 记录类目信息获取请求（保存完整响应数据）
        add_request_record(
            request_type="get_category_info_from_scraped_data",
            url="seller_tree_api",
            data={"product_name": scraped_data.get('name', ''), "sku": sku},
            response=full_category_response,  # 保存完整的API响应
            success=bool(category_result),
            error=None if category_result else "Seller Tree API 获取类目失败"
        )

        if category_result:
            category_id, type_id = category_result
            print(f"通过 Seller Tree API 确定类目: ID={category_id}, Type={type_id}")
        else:
            # 回退到原有的类目推断逻辑
            print("Seller Tree API 获取类目失败，使用传统方法推断类目...")
            category_path = extract_category_path_from_scraped_data(scraped_data)
            print(f"   推断的类目路径: {' > '.join(category_path)}")

            category_result = api_client.find_category_by_path(category_tree, category_path)
            if not category_result:
                print("无法确定商品类目，请手动指定")
                print("可用的主要类目:")
                for i, cat in enumerate(category_tree[:10]):  # 显示前10个主类目
                    category_name = cat.get('category_name', cat.get('title', 'Unknown'))
                    print(f"   {i+1}. {category_name}")

                # 提供手动选择选项
                print("\n请选择一个类目编号，或输入 'skip' 跳过类目验证继续演示:")
                choice = input("选择: ").strip()

                if choice.lower() == 'skip':
                    print("跳过类目验证，使用模拟数据继续演示...")
                    # 使用一个有效的演示类目（从类目树中找到的有效类目）
                    category_id, type_id = 17027920, 92699  # 有效的类目：Моющие и чистящие средства -> Отбеливатель
                    print(f"使用演示类目: ID={category_id}, Type={type_id}")
                else:
                    try:
                        choice_idx = int(choice) - 1
                        if 0 <= choice_idx < len(category_tree):
                            selected_cat = category_tree[choice_idx]
                            print(f"您选择了: {selected_cat.get('category_name', 'Unknown')}")
                            print("此功能需要进一步开发以选择具体的子类目...")
                            return
                        else:
                            print("无效的选择")
                            return
                    except ValueError:
                        print("无效的输入")
                        return
            else:
                category_id, type_id = category_result
                print(f"通过传统方法确定类目: ID={category_id}, Type={type_id}")

        # 获取类目属性
        print("获取类目属性...")
        api_attributes = api_client.get_category_attributes(category_id, type_id)

        # 记录类目属性获取请求（保存完整响应数据）
        add_request_record(
            request_type="get_category_attributes",
            url=f"{config.API_BASE_URL}/v1/description-category/attribute",
            data={"description_category_id": category_id, "type_id": type_id},
            response=api_attributes,  # 保存完整的属性数据
            success=bool(api_attributes),
            error=None if api_attributes else "获取类目属性失败"
        )

        if not api_attributes:
            print("获取类目属性失败")
            return

        required_attrs = [attr for attr in api_attributes if attr.get('is_required')]
        print(f"获取到 {len(api_attributes)} 个属性，其中 {len(required_attrs)} 个必填")

        # 数据映射
        print("映射商品数据...")
        mapped_payload = map_scraped_to_api_payload(
            scraped_data, category_id, type_id, api_attributes, api_client
        )

        if not mapped_payload:
            print("数据映射失败")
            return

        print("数据映射成功")

        # 验证数据
        print("验证商品数据...")
        is_valid, errors = api_client.validate_product_data(mapped_payload[0], required_attrs)
        if not is_valid:
            print("数据验证失败:")
            for error in errors:
                print(f"   - {error}")
            return

        print("数据验证通过")

        # 确认创建
        print("\n" + "="*50)
        print("商品信息预览:")
        item = mapped_payload[0]
        print(f"   商品名称: {item['name']}")
        print(f"   商品价格: {item['price']} RUB")
        print(f"   商品编码: {item['offer_id']}")
        print(f"   类目ID: {item['description_category_id']}")
        print(f"   图片数量: {len(item['images'])}")
        print(f"   属性数量: {len(item['attributes'])}")
        print("="*50)

        confirm = input("\n是否确认创建此商品？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', 'да']:
            print("用户取消操作")
            return

        # 创建商品
        print("创建商品...")
        try:
            result = product_manager.create_product_with_monitoring(mapped_payload[0], wait_for_completion=True)

            # 记录商品创建请求
            add_request_record(
                request_type="create_product",
                url=f"{config.API_BASE_URL}/v2/product/import",
                data={
                    "offer_id": mapped_payload[0].get('offer_id'),
                    "name": mapped_payload[0].get('name'),
                    "category_id": mapped_payload[0].get('description_category_id'),
                    "price": mapped_payload[0].get('price')
                },
                response=result,
                success=result.get("success", False) and result.get("creation_success", False),
                error=result.get('error') if not result.get("success", False) else None
            )

            if result["success"]:
                if result.get("creation_success"):
                    print("🎉 商品创建成功！")
                    if result.get("product_id"):
                        print(f"   商品ID: {result['product_id']}")
                        print(f"   商品链接: https://seller.ozon.ru/app/products/{result['product_id']}")

                        # 提供后续管理选项
                        print("\n后续操作选项:")
                        print("1. 运行商品管理工具: python product_management_tool.py")
                        print("2. 查看商品状态")
                        print("3. 更新价格或库存")
                else:
                    print("商品创建失败")
                    if result.get("errors"):
                        print("错误详情:")
                        for error in result["errors"]:
                            print(f"   - {error}")
            else:
                print("商品创建请求失败")
                print(f"错误: {result.get('error', '未知错误')}")

        except Exception as e:
            error_msg = f"创建商品时出错: {e}"
            print(error_msg)

            # 记录异常
            add_request_record(
                request_type="create_product",
                url=f"{config.API_BASE_URL}/v2/product/import",
                data={"offer_id": mapped_payload[0].get('offer_id')},
                response=None,
                success=False,
                error=error_msg
            )

            import traceback
            traceback.print_exc()

    except KeyboardInterrupt:
        print("\n程序被用户中断")
        add_request_record(
            request_type="program_interrupt",
            success=False,
            error="程序被用户中断"
        )
    except Exception as e:
        error_msg = f"程序执行出错: {e}"
        print(error_msg)
        add_request_record(
            request_type="program_error",
            success=False,
            error=error_msg
        )
        import traceback
        traceback.print_exc()
    finally:
        # 显示本次运行请求统计
        print("\n📊 本次运行请求统计:")
        print_request_summary()

if __name__ == "__main__":
    import time
    main()
