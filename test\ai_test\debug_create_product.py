#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试商品创建API的程序
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
import config

def debug_create_product():
    """调试商品创建API"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 创建一个最简单的商品载荷
        simple_payload = {
            "items": [{
                "offer_id": "TEST_SKU_123456",
                "name": "Test Product",
                "category_id": 53968796,  # 使用已知的类目ID
                "price": "100",
                "vat": "0.20",
                "images": ["https://cdn1.ozone.ru/s3/multimedia-1/6123456789.jpg"],
                "attributes": [
                    {
                        "id": 85,  # 品牌
                        "complex_id": 0,
                        "values": [{"dictionary_value_id": 5474948}]  # Brand
                    },
                    {
                        "id": 8229,  # 类型
                        "complex_id": 0,
                        "values": [{"dictionary_value_id": 971080300}]  # Аксессуар для робота-пылесоса
                    },
                    {
                        "id": 9048,  # 模型名称
                        "complex_id": 0,
                        "values": [{"value": "Test Model"}]
                    }
                ],
                "depth": 100,
                "width": 100,
                "height": 50,
                "dimension_unit": "mm",
                "weight": 200,
                "weight_unit": "g"
            }]
        }
        
        print("尝试创建简单商品...")
        print(f"载荷: {json.dumps(simple_payload, indent=2, ensure_ascii=False)}")
        
        try:
            # 直接调用API
            response = api_client._post("/v3/product/import", simple_payload)
            print(f"成功! 响应: {response}")
            
        except Exception as e:
            print(f"创建失败: {e}")
            
            # 尝试获取更详细的错误信息
            if hasattr(e, 'response'):
                print(f"状态码: {e.response.status_code}")
                try:
                    error_json = e.response.json()
                    print(f"错误详情 (JSON): {json.dumps(error_json, indent=2, ensure_ascii=False)}")
                except:
                    print(f"错误详情 (文本): {e.response.text}")
            
            # 尝试更简单的载荷
            print("\n尝试更简单的载荷...")
            minimal_payload = {
                "items": [{
                    "offer_id": "MINIMAL_TEST_123",
                    "name": "Minimal Test Product",
                    "category_id": 53968796,
                    "price": "100",
                    "vat": "0.20",
                    "attributes": [
                        {
                            "id": 85,  # 品牌
                            "complex_id": 0,
                            "values": [{"dictionary_value_id": 5474948}]
                        },
                        {
                            "id": 8229,  # 类型
                            "complex_id": 0,
                            "values": [{"dictionary_value_id": 971080300}]
                        },
                        {
                            "id": 9048,  # 模型名称
                            "complex_id": 0,
                            "values": [{"value": "Minimal Model"}]
                        }
                    ]
                }]
            }
            
            try:
                response = api_client._post("/v3/product/import", minimal_payload)
                print(f"最简载荷成功! 响应: {response}")
            except Exception as e2:
                print(f"最简载荷也失败: {e2}")
                if hasattr(e2, 'response'):
                    try:
                        error_json = e2.response.json()
                        print(f"最简载荷错误详情: {json.dumps(error_json, indent=2, ensure_ascii=False)}")
                    except:
                        print(f"最简载荷错误文本: {e2.response.text}")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序"""
    print("=" * 60)
    print("调试商品创建API")
    print("=" * 60)
    
    debug_create_product()

if __name__ == "__main__":
    main()
