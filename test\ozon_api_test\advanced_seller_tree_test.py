#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级 Ozon Seller Tree API 测试
使用配置文件和多种测试场景
"""

import json
import os
import sys
from datetime import datetime
from curl_cffi import requests

# 导入测试配置
from test_config import (
    get_full_url, 
    get_headers_with_cookie, 
    get_test_payload,
    TEST_SCENARIOS,
    BROWSER_FINGERPRINTS,
    API_CONFIG,
    OUTPUT_CONFIG
)

class AdvancedOzonAPITester:
    """高级 Ozon API 测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.api_url = get_full_url()
        self.session = requests.Session()
        self.results = []
        self.setup_output_dir()
    
    def setup_output_dir(self):
        """设置输出目录"""
        if OUTPUT_CONFIG["save_results"]:
            os.makedirs(OUTPUT_CONFIG["results_dir"], exist_ok=True)
    
    def run_single_test(self, scenario_name, browser_fingerprint="chrome110"):
        """
        运行单个测试场景
        
        Args:
            scenario_name (str): 测试场景名称
            browser_fingerprint (str): 浏览器指纹
        
        Returns:
            dict: 测试结果
        """
        scenario = TEST_SCENARIOS.get(scenario_name)
        if not scenario:
            return {"error": f"未知测试场景: {scenario_name}"}
        
        print(f"\n🧪 运行测试: {scenario['description']}")
        print(f"   场景: {scenario_name}")
        print(f"   浏览器指纹: {browser_fingerprint}")
        print(f"   SKUs: {scenario['skus']}")
        
        # 准备请求
        headers = get_headers_with_cookie()
        payload = get_test_payload(scenario_name)
        
        test_result = {
            "timestamp": datetime.now().isoformat(),
            "scenario": scenario_name,
            "description": scenario["description"],
            "browser_fingerprint": browser_fingerprint,
            "request": {
                "url": self.api_url,
                "headers": {k: v for k, v in headers.items() if k != 'Cookie'},  # 隐藏敏感信息
                "payload": payload
            },
            "response": {},
            "success": False,
            "error": None
        }
        
        try:
            # 发送请求
            response = self.session.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=API_CONFIG["timeout"],
                impersonate=browser_fingerprint,
                verify=True
            )
            
            # 记录响应
            test_result["response"] = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content_length": len(response.text),
                "content_type": response.headers.get("content-type", "unknown")
            }
            
            # 判断是否成功
            expected_statuses = scenario.get("expected_status", [200])
            test_result["success"] = response.status_code in expected_statuses
            
            # 处理响应内容
            if response.status_code == 200:
                try:
                    json_data = response.json()
                    test_result["response"]["data"] = json_data
                    print(f"   ✅ 成功 (状态码: {response.status_code})")
                    if OUTPUT_CONFIG["pretty_print"]:
                        print(f"   📄 响应数据: {json.dumps(json_data, indent=2, ensure_ascii=False)}")
                except json.JSONDecodeError:
                    test_result["response"]["raw_content"] = response.text[:500]
                    print(f"   ✅ 成功 (状态码: {response.status_code}, 非JSON响应)")
            else:
                test_result["response"]["error_content"] = response.text[:500]
                if test_result["success"]:
                    print(f"   ⚠️ 预期状态码 (状态码: {response.status_code})")
                else:
                    print(f"   ❌ 失败 (状态码: {response.status_code})")
                    print(f"   错误内容: {response.text[:200]}...")
            
        except Exception as e:
            test_result["error"] = str(e)
            print(f"   ❌ 异常: {e}")
        
        self.results.append(test_result)
        return test_result
    
    def run_all_scenarios(self, browser_fingerprint="chrome110"):
        """运行所有测试场景"""
        print(f"🚀 开始运行所有测试场景 (浏览器指纹: {browser_fingerprint})")
        print("=" * 60)
        
        for scenario_name in TEST_SCENARIOS.keys():
            self.run_single_test(scenario_name, browser_fingerprint)
        
        self.print_summary()
    
    def run_browser_fingerprint_tests(self, scenario_name="basic"):
        """测试不同的浏览器指纹"""
        print(f"🔍 测试不同浏览器指纹 (场景: {scenario_name})")
        print("=" * 60)
        
        for fingerprint in BROWSER_FINGERPRINTS:
            self.run_single_test(scenario_name, fingerprint)
        
        self.print_summary()
    
    def print_summary(self):
        """打印测试总结"""
        if not self.results:
            print("\n❌ 没有测试结果")
            return
        
        total_tests = len(self.results)
        successful_tests = len([r for r in self.results if r["success"]])
        failed_tests = total_tests - successful_tests
        
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests)*100:.1f}%")
        
        # 按状态码分组
        status_codes = {}
        for result in self.results:
            status = result["response"].get("status_code", "异常")
            status_codes[status] = status_codes.get(status, 0) + 1
        
        print(f"\n状态码分布:")
        for status, count in sorted(status_codes.items()):
            print(f"  {status}: {count} 次")
        
        # 显示失败的测试
        failed_results = [r for r in self.results if not r["success"]]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results:
                error_msg = result.get("error") or result["response"].get("error_content", "未知错误")[:100]
                print(f"  - {result['scenario']}: {error_msg}")
    
    def save_results(self):
        """保存测试结果到文件"""
        if not OUTPUT_CONFIG["save_results"]:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(
            OUTPUT_CONFIG["results_dir"], 
            f"ozon_seller_tree_test_{timestamp}.json"
        )
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump({
                    "test_info": {
                        "timestamp": datetime.now().isoformat(),
                        "api_url": self.api_url,
                        "total_tests": len(self.results),
                        "successful_tests": len([r for r in self.results if r["success"]])
                    },
                    "results": self.results
                }, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存结果失败: {e}")

def main():
    """主函数"""
    print("🧪 高级 Ozon Seller Tree API 测试工具")
    print("=" * 60)
    
    tester = AdvancedOzonAPITester()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "all":
            # 运行所有场景
            tester.run_all_scenarios()
        elif command == "fingerprints":
            # 测试不同浏览器指纹
            tester.run_browser_fingerprint_tests()
        elif command in TEST_SCENARIOS:
            # 运行特定场景
            tester.run_single_test(command)
        else:
            print(f"❌ 未知命令: {command}")
            print(f"可用命令: all, fingerprints, {', '.join(TEST_SCENARIOS.keys())}")
            return
    else:
        # 默认运行基本测试
        print("运行基本测试 (使用 'python advanced_seller_tree_test.py all' 运行所有测试)")
        tester.run_single_test("basic")
    
    # 保存结果
    tester.save_results()

if __name__ == "__main__":
    main()
