# product_management_tool.py
"""
商品管理工具 - 提供商品管理的命令行界面
"""

import sys
from typing import List, Dict
from modules.api_client import OzonApiClient
from modules.product_manager import ProductManager
import config

def print_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("           OZON 商品管理工具")
    print("="*60)
    print("1. 查看商品列表")
    print("2. 查询商品状态")
    print("3. 更新商品价格")
    print("4. 更新商品库存")
    print("5. 存档商品")
    print("6. 恢复商品")
    print("7. 删除商品")
    print("8. 获取商品报告")
    print("9. 批量更新属性")
    print("0. 退出")
    print("="*60)

def get_product_list(product_manager: ProductManager):
    """获取并显示商品列表"""
    print("\n📦 获取商品列表...")
    
    # 获取过滤选项
    print("过滤选项（直接回车跳过）:")
    visibility = input("可见性 (ALL/VISIBLE/INVISIBLE): ").strip()
    state = input("状态 (processed/moderating/declined/failed): ").strip()
    
    filters = {}
    if visibility:
        filters["visibility"] = visibility
    if state:
        filters["state"] = state
    
    products = product_manager.get_product_list_with_filters(filters, limit=20)
    
    if products:
        print(f"\n找到 {len(products)} 个商品:")
        print("-" * 80)
        print(f"{'ID':<10} {'SKU':<20} {'名称':<30} {'状态':<15}")
        print("-" * 80)
        
        for product in products:
            product_id = product.get("product_id", "N/A")
            offer_id = product.get("offer_id", "N/A")
            name = product.get("name", "N/A")[:28]
            state = product.get("state", "N/A")
            
            print(f"{product_id:<10} {offer_id:<20} {name:<30} {state:<15}")
    else:
        print("❌ 未找到商品")

def query_product_status(product_manager: ProductManager):
    """查询商品状态"""
    print("\n🔍 查询商品状态...")
    
    choice = input("查询方式 (1-商品ID, 2-SKU): ").strip()
    
    if choice == "1":
        product_id = input("请输入商品ID: ").strip()
        if product_id.isdigit():
            status = product_manager.get_product_status(product_id=int(product_id))
        else:
            print("❌ 商品ID必须是数字")
            return
    elif choice == "2":
        offer_id = input("请输入商品SKU: ").strip()
        if offer_id:
            status = product_manager.get_product_status(offer_id=offer_id)
        else:
            print("❌ SKU不能为空")
            return
    else:
        print("❌ 无效选择")
        return
    
    if status:
        print("\n商品状态信息:")
        print("-" * 50)
        print(f"商品ID: {status.get('product_id', 'N/A')}")
        print(f"SKU: {status.get('offer_id', 'N/A')}")
        print(f"名称: {status.get('name', 'N/A')}")
        print(f"状态: {status.get('state', 'N/A')}")
        print(f"可见性: {status.get('visibility', 'N/A')}")
        print(f"价格: {status.get('price', 'N/A')}")
        print(f"库存: {status.get('stocks', 'N/A')}")
    else:
        print("❌ 未找到商品信息")

def update_product_price(product_manager: ProductManager):
    """更新商品价格"""
    print("\n💰 更新商品价格...")
    
    product_id = input("请输入商品ID: ").strip()
    if not product_id.isdigit():
        print("❌ 商品ID必须是数字")
        return
    
    new_price = input("请输入新价格: ").strip()
    try:
        new_price = float(new_price)
    except ValueError:
        print("❌ 价格必须是数字")
        return
    
    old_price = input("请输入原价（可选，直接回车跳过）: ").strip()
    old_price_val = None
    if old_price:
        try:
            old_price_val = float(old_price)
        except ValueError:
            print("❌ 原价必须是数字")
            return
    
    success = product_manager.update_product_price(int(product_id), new_price, old_price_val)
    if success:
        print("✅ 价格更新成功")
    else:
        print("❌ 价格更新失败")

def update_product_stock(product_manager: ProductManager):
    """更新商品库存"""
    print("\n📦 更新商品库存...")
    
    product_id = input("请输入商品ID: ").strip()
    if not product_id.isdigit():
        print("❌ 商品ID必须是数字")
        return
    
    stock_quantity = input("请输入库存数量: ").strip()
    try:
        stock_quantity = int(stock_quantity)
    except ValueError:
        print("❌ 库存数量必须是整数")
        return
    
    success = product_manager.update_product_stock(int(product_id), stock_quantity)
    if success:
        print("✅ 库存更新成功")
    else:
        print("❌ 库存更新失败")

def archive_products(product_manager: ProductManager):
    """存档商品"""
    print("\n📁 存档商品...")
    
    product_ids_str = input("请输入商品ID（多个用逗号分隔）: ").strip()
    try:
        product_ids = [int(pid.strip()) for pid in product_ids_str.split(",") if pid.strip()]
    except ValueError:
        print("❌ 商品ID必须是数字")
        return
    
    if not product_ids:
        print("❌ 请输入至少一个商品ID")
        return
    
    confirm = input(f"确认存档 {len(product_ids)} 个商品？(y/N): ").strip().lower()
    if confirm == 'y':
        result = product_manager.archive_products(product_ids)
        if result.get("success", True):
            print("✅ 商品存档成功")
        else:
            print("❌ 商品存档失败")
    else:
        print("❌ 操作已取消")

def main():
    """主程序"""
    try:
        # 验证配置
        config.validate_config()
        
        # 初始化API客户端
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        if not api_client.test_api_connection():
            print("❌ API连接失败，请检查配置")
            return
        
        # 初始化商品管理器
        product_manager = ProductManager(api_client)
        
        while True:
            print_menu()
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                get_product_list(product_manager)
            elif choice == "2":
                query_product_status(product_manager)
            elif choice == "3":
                update_product_price(product_manager)
            elif choice == "4":
                update_product_stock(product_manager)
            elif choice == "5":
                archive_products(product_manager)
            elif choice == "6":
                print("🔄 恢复商品功能开发中...")
            elif choice == "7":
                print("🗑️ 删除商品功能开发中...")
            elif choice == "8":
                print("📊 商品报告功能开发中...")
            elif choice == "9":
                print("🔧 批量更新功能开发中...")
            else:
                print("❌ 无效选择，请重新输入")
            
            input("\n按回车键继续...")
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
