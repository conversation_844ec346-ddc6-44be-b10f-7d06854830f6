# Ozon Seller API 接口列表

生成时间: 2025-07-23 21:39:54

## CategoryAPI (4 个接口)

1. **[POST]** `/v1/description-category/tree` - 商品类别和类型的树形图
2. **[POST]** `/v1/description-category/attribute` - 类别特征列表
3. **[POST]** `/v1/description-category/attribute/values` - 特征值指南
4. **[POST]** `/v1/description-category/attribute/values/search` - 根据属性的参考值进行搜索

## ProductAPI (19 个接口)

5. **[POST]** `/v3/product/import` - 创建或更新商品
6. **[POST]** `/v1/product/import/info` - 查询商品添加或更新状态
7. **[POST]** `/v1/product/import-by-sku` - 通过SKU创建商品
8. **[POST]** `/v1/product/attributes/update` - 更新商品特征
9. **[POST]** `/v1/product/pictures/import` - 上传或更新商品图片
10. **[POST]** `/v3/product/list` - 品列表的
11. **[POST]** `/v1/product/rating-by-sku` - 按SKU获得商品的内容排名
12. **[POST]** `/v3/product/info/list` - 根据标识符获取商品信息
13. **[POST]** `/v3/products/info/attributes` - 获取商品特征描述
14. **[POST]** `/v4/product/info/attributes` - 获取商品特征描述
15. **[POST]** `/v1/product/info/description` - 获取商品详细信息
16. **[POST]** `/v4/product/info/limit` - 品类限制、商品的创建和更新
17. **[POST]** `/v1/product/update/offer-id` - 从卖家的系统中改变商品货号
18. **[POST]** `/v1/product/archive` - 将商品归档
19. **[POST]** `/v1/product/unarchive` - 从档案中还原商品
20. **[POST]** `/v2/products/delete` - 从存档删除没有SKU的商品
21. **[POST]** `/v1/product/info/subscription` - 订阅该商品的用户数
22. **[POST]** `/v1/product/related-sku/get` - 获取相关SKU
23. **[POST]** `/v2/product/pictures/info` - 获取商品图片

## BarcodeAPI (2 个接口)

24. **[POST]** `/v1/barcode/add` - 为商品绑定条形码
25. **[POST]** `/v1/barcode/generate` - 创建商品条形码

## Prices&StocksAPI (7 个接口)

26. **[POST]** `/v2/products/stocks` - 更新库存商品的数量
27. **[POST]** `/v4/product/info/stocks` - 关于商品数量的信息
28. **[POST]** `/v1/product/info/stocks-by-warehouse/fbs` - 关于卖家库存余额的信息
29. **[POST]** `/v1/product/import/prices` - 更新价格
30. **[POST]** `/v5/product/info/prices` - 获取商品价格信息
31. **[POST]** `/v1/product/info/discounted` - 通过减价商品的SKU查找减价商品和主商品的信息
32. **[POST]** `/v1/product/update/discount` - 为打折商品设置折扣

## Promos (8 个接口)

33. **[GET]** `/v1/actions` - 活动清单
34. **[POST]** `/v1/actions/candidates` - 可用的促销商品清单
35. **[POST]** `/v1/actions/products` - 参与 活动的商品列表
36. **[POST]** `/v1/actions/products/activate` - 在促销活动中增加一个商品
37. **[POST]** `/v1/actions/products/deactivate` - 从活动中删除商品
38. **[POST]** `/v1/actions/discounts-task/list` - 申请折扣列表
39. **[POST]** `/v1/actions/discounts-task/approve` - 同意折扣申请
40. **[POST]** `/v1/actions/discounts-task/decline` - 取消折扣申请

## PricingStrategyAPI (12 个接口)

41. **[POST]** `/v1/pricing-strategy/competitors/list` - 竞争对手名单
42. **[POST]** `/v1/pricing-strategy/list` - 策略列表
43. **[POST]** `/v1/pricing-strategy/create` - 创建策略
44. **[POST]** `/v1/pricing-strategy/info` - 策略信息
45. **[POST]** `/v1/pricing-strategy/update` - 更新策略
46. **[POST]** `/v1/pricing-strategy/products/add` - 将商品添加到策略
47. **[POST]** `/v1/pricing-strategy/strategy-ids-by-product-ids` - 策略ID列表
48. **[POST]** `/v1/pricing-strategy/products/list` - 策略中的商品列表
49. **[POST]** `/v1/pricing-strategy/product/info` - 竞争对手  的商品价格
50. **[POST]** `/v1/pricing-strategy/products/delete` - 从策略中删除商品
51. **[POST]** `/v1/pricing-strategy/status` - 更改策略状态
52. **[POST]** `/v1/pricing-strategy/delete` - 删除策略

## WarehouseAPI (2 个接口)

53. **[POST]** `/v1/warehouse/list` - 仓库清单
54. **[POST]** `/v1/delivery-method/list` - 仓库物流方式清单

## PolygonAPI (2 个接口)

55. **[POST]** `/v1/polygon/create` - 创建一个快递的设施
56. **[POST]** `/v1/polygon/bind` - 将快递方式与快递设施联系起来

## FBS&rFBSMarks (7 个接口)

57. **[POST]** `/v5/fbs/posting/product/exemplar/create-or-get` - 获取商品实例信息
58. **[POST]** `/v4/fbs/posting/product/exemplar/validate` - 标志代码验证
59. **[POST]** `/v4/fbs/posting/product/exemplar/set` - 检查并保存份数数据
60. **[POST]** `/v5/fbs/posting/product/exemplar/set` - 检查并保存份数数据 (第5方案)
61. **[POST]** `/v4/fbs/posting/product/exemplar/status` - 获取样件添加状态
62. **[POST]** `/v4/posting/fbs/ship` - 搜集订单 (第4方案)
63. **[POST]** `/v4/posting/fbs/ship/package` - 货件的部分装配 (第4方案)

## DeliveryFBS (2 个接口)

64. **[POST]** `/v1/carriage/get` - 运输信息
65. **[POST]** `/v1/posting/carriage-available/list` - 可供运输的列表

## DeliveryrFBS (6 个接口)

66. **[POST]** `/v2/fbs/posting/delivering` - 将状态改成“运输中”
67. **[POST]** `/v2/fbs/posting/tracking-number/set` - 添加跟踪号
68. **[POST]** `/v2/fbs/posting/last-mile` - 状态改为“最后一英里”
69. **[POST]** `/v2/fbs/posting/delivered` - 将状态改成“已送达”
70. **[POST]** `/v2/fbs/posting/sent-by-seller` - 将状态改为“由卖家发送”
71. **[POST]** `/v1/posting/cutoff/set` - 确认货件发运日期

## Pass (8 个接口)

72. **[POST]** `/v1/pass/list` - 通行证列表
73. **[POST]** `/v1/carriage/pass/create` - 创建通行证
74. **[POST]** `/v1/carriage/pass/update` - 更新通行证
75. **[POST]** `/v1/carriage/pass/delete` - 删除通行证
76. **[POST]** `/v1/return/pass/create` - 创建退货通行证
77. **[POST]** `/v1/return/pass/update` - 更新退货通行证
78. **[POST]** `/v1/return/pass/delete` - 删除退货通行证
79. **[POST]** `/v1/returns/company/fbs/info` - FBS退货数量

## FBS (13 个接口)

80. **[POST]** `/v3/posting/fbs/unfulfilled/list` - 未处理货件列表 （第三 版）
81. **[POST]** `/v3/posting/fbs/list` - 货件列表（第三版）
82. **[POST]** `/v3/posting/fbs/get` - 按照ID获取货件信息
83. **[POST]** `/v2/posting/fbs/get-by-barcode` - 按条形码获取有关货件的信息
84. **[POST]** `/v2/posting/fbs/product/country/list` - 可用产地名单
85. **[POST]** `/v2/posting/fbs/product/country/set` - 添加商品产地信息
86. **[POST]** `/v2/posting/fbs/package-label` - 打印标签
87. **[POST]** `/v2/posting/fbs/awaiting-delivery` - 货件装运
88. **[POST]** `/v2/posting/fbs/cancel-reason/list` - 货件取消原因
89. **[POST]** `/v1/posting/fbs/cancel-reason` - 货运取消原因
90. **[POST]** `/v2/posting/fbs/cancel` - 取消货运
91. **[POST]** `/v2/posting/fbs/product/change` - 为货件中的称重商品添加重量
92. **[POST]** `/v2/posting/fbs/product/cancel` - 取消某些商品发货

## ReturnsAPI (1 个接口)

93. **[POST]** `/v1/returns/list` - FBO和FBS退货信息

## CancellationAPI (7 个接口)

94. **[POST]** `/v1/conditional-cancellation/get` - 获取有关rFBS取消订单的消息
95. **[POST]** `/v2/conditional-cancellation/list` - 获取 rFBS 取消申请列表
96. **[POST]** `/v1/conditional-cancellation/list` - 获取rFBS取消申请列表
97. **[POST]** `/v2/conditional-cancellation/approve` - 确认 rFBS 取消申请
98. **[POST]** `/v1/conditional-cancellation/approve` - 确定rFBS取消订单
99. **[POST]** `/v2/conditional-cancellation/reject` - 拒绝 rFBS 取消申请
100. **[POST]** `/v1/conditional-cancellation/reject` - 拒绝取消rFBS申请

## RFBSReturnsAPI (8 个接口)

101. **[POST]** `/v2/returns/rfbs/list` - 退货申请列表
102. **[POST]** `/v2/returns/rfbs/get` - 退货申请信息
103. **[POST]** `/v2/returns/rfbs/reject` - 拒绝退货申请
104. **[POST]** `/v2/returns/rfbs/compensate` - 退还部分商品金额
105. **[POST]** `/v2/returns/rfbs/verify` - 批准退货申请
106. **[POST]** `/v2/returns/rfbs/receive-return` - 确认收到待检查商品
107. **[POST]** `/v2/returns/rfbs/return-money` - 向买家退款
108. **[POST]** `/v1/returns/rfbs/action/set` - 传递 rFBS  退货的可用操作

## ChatAPI (7 个接口)

109. **[POST]** `/v1/chat/send/message` - 发送信息
110. **[POST]** `/v1/chat/send/file` - 发送文件
111. **[POST]** `/v1/chat/start` - 创建新聊天
112. **[POST]** `/v2/chat/list` - 聊天清单
113. **[POST]** `/v2/chat/history` - 聊天历史记录
114. **[POST]** `/v3/chat/history` - 聊天历史记录
115. **[POST]** `/v2/chat/read` - 将信息标记为已读

## ReportAPI (7 个接口)

116. **[POST]** `/v1/report/info` - 报告信息
117. **[POST]** `/v1/report/list` - 报告清单
118. **[POST]** `/v1/report/products/create` - 商品报告
119. **[POST]** `/v1/report/postings/create` - 发货报告
120. **[POST]** `/v1/finance/cash-flow-statement/list` - 财务报告
121. **[POST]** `/v1/report/discounted/create` - 减价商品报告
122. **[POST]** `/v1/report/warehouse/stock` - 关于FBS仓库库存报告

## AnalyticsAPI (3 个接口)

123. **[POST]** `/v1/analytics/data` - 分析数据
124. **[POST]** `/v1/analytics/product-queries` - 获取商品搜索查询信息
125. **[POST]** `/v1/analytics/product-queries/details` - 有关特定商品查询的信息

## FinanceAPI (7 个接口)

126. **[POST]** `/v2/finance/realization` - 商品销售报告 （第2版）
127. **[POST]** `/v1/finance/realization/posting` - 按订单细分的商品销售报告
128. **[POST]** `/v3/finance/transaction/list` - 交易清单
129. **[POST]** `/v3/finance/transaction/totals` - 清单数目
130. **[POST]** `/v1/finance/realization/by-day` - 每日商品销售报告
131. **[POST]** `/v1/finance/compensation` - 赔偿报告
132. **[POST]** `/v1/finance/decompensation` - 赔偿返还报告

## BetaMethod (5 个接口)

133. **[POST]** `/v1/posting/fbs/split` - 将订单拆分为不带备货的货件
134. **[POST]** `/v1/analytics/average-delivery-time` - 获取平均配送时间的分析数据
135. **[POST]** `/v1/analytics/average-delivery-time/details` - 获取平均配送时间的详细分析
136. **[POST]** `/v1/roles` - 使用API密钥获取角色和方式列表
137. **[POST]** `/v1/product/info/wrong-volume` - 体积重量特征不正确的商品列表

## Examples (5 个接口)

138. **[POST]** `/v6/fbs/posting/product/exemplar/set` - 检查并保存份数数据
139. **[POST]** `/v6/fbs/posting/product/exemplar/create-or-get` - 获取已创建样件数据
140. **[POST]** `/v5/fbs/posting/product/exemplar/status` - 获取样件添加状态
141. **[POST]** `/v5/fbs/posting/product/exemplar/validate` - 标志代码验证
142. **[POST]** `/v1/fbs/posting/product/exemplar/update` - Обновить данные экземпляров

## ReviewAPI (7 个接口)

143. **[POST]** `/v1/review/comment/create` - 对评价留下评论
144. **[POST]** `/v1/review/comment/delete` - 删除对评价的评论
145. **[POST]** `/v1/review/comment/list` - 评价的评论列表
146. **[POST]** `/v1/review/change-status` - 更改评价状态
147. **[POST]** `/v1/review/count` - 根据状态统计的评价数量
148. **[POST]** `/v1/review/info` - 获取评价信息
149. **[POST]** `/v1/review/list` - 获取评价列表

**总计: 149 个接口**