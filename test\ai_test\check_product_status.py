#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询商品上架状态的程序
"""

import sys
import os
import time
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.logger import main_logger
import config

def format_status_message(status: str) -> str:
    """格式化状态消息"""
    status_map = {
        "imported": "已导入 - 商品已成功创建",
        "failed": "失败 - 商品创建失败",
        "processing": "处理中 - 正在创建商品",
        "pending": "等待中 - 任务排队等待处理"
    }
    return status_map.get(status, f"未知状态: {status}")

def format_error_message(errors: list) -> str:
    """格式化错误消息"""
    if not errors:
        return "无错误"
    
    error_messages = []
    for error in errors:
        if isinstance(error, dict):
            code = error.get('code', '未知')
            message = error.get('message', '无详细信息')
            error_messages.append(f"错误代码 {code}: {message}")
        else:
            error_messages.append(str(error))
    
    return "; ".join(error_messages)

def check_single_task_status(api_client: OzonApiClient, task_id: int):
    """查询单个任务的状态"""
    try:
        print(f"查询任务ID: {task_id}")
        print("-" * 50)
        
        # 调用API查询状态
        result = api_client.check_import_status(task_id)
        
        if not result:
            print("未获取到状态信息")
            return
        
        # 解析响应
        response_data = result.get("result", {})
        items = response_data.get("items", [])
        total = response_data.get("total", 0)
        
        print(f"任务总数: {total}")
        print(f"返回商品数: {len(items)}")
        print()
        
        if not items:
            print("该任务下没有商品信息")
            return
        
        # 显示每个商品的状态
        for i, item in enumerate(items, 1):
            print(f"商品 {i}:")
            
            # 基本信息
            offer_id = item.get("offer_id", "未知")
            product_id = item.get("product_id", "未分配")
            status = item.get("status", "未知")
            
            print(f"  Offer ID: {offer_id}")
            print(f"  Product ID: {product_id}")
            print(f"  状态: {format_status_message(status)}")
            
            # 错误信息
            errors = item.get("errors", [])
            if errors:
                print(f"  错误: {format_error_message(errors)}")
            else:
                print(f"  错误: 无")
            
            # 创建时间
            created_at = item.get("created_at")
            if created_at:
                print(f"  创建时间: {created_at}")
            
            # 更新时间
            updated_at = item.get("updated_at")
            if updated_at:
                print(f"  更新时间: {updated_at}")
            
            print()
        
        # 状态汇总
        status_counts = {}
        for item in items:
            status = item.get("status", "未知")
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print("状态汇总:")
        for status, count in status_counts.items():
            print(f"  {format_status_message(status)}: {count} 个")
        
    except Exception as e:
        print(f"查询状态失败: {e}")
        import traceback
        traceback.print_exc()

def monitor_task_status(api_client: OzonApiClient, task_id: int, max_wait_minutes: int = 10):
    """监控任务状态直到完成"""
    print(f"开始监控任务 {task_id}，最大等待时间: {max_wait_minutes} 分钟")
    print("=" * 60)
    
    start_time = time.time()
    max_wait_seconds = max_wait_minutes * 60
    check_interval = 30  # 每30秒检查一次
    
    while True:
        elapsed_time = time.time() - start_time
        
        print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 检查状态... (已等待 {elapsed_time:.0f}s)")
        
        try:
            result = api_client.check_import_status(task_id)
            response_data = result.get("result", {})
            items = response_data.get("items", [])
            
            if not items:
                print("暂无商品信息，继续等待...")
            else:
                # 检查所有商品的状态
                all_completed = True
                status_summary = {}
                
                for item in items:
                    status = item.get("status", "未知")
                    status_summary[status] = status_summary.get(status, 0) + 1
                    
                    if status in ["processing", "pending"]:
                        all_completed = False
                
                # 显示当前状态
                print("当前状态:")
                for status, count in status_summary.items():
                    print(f"  {format_status_message(status)}: {count} 个")
                
                # 如果所有商品都完成了处理
                if all_completed:
                    print("\n所有商品处理完成!")
                    check_single_task_status(api_client, task_id)
                    break
            
            # 检查是否超时
            if elapsed_time >= max_wait_seconds:
                print(f"\n已达到最大等待时间 ({max_wait_minutes} 分钟)，停止监控")
                print("最终状态:")
                check_single_task_status(api_client, task_id)
                break
            
            # 等待下次检查
            print(f"等待 {check_interval} 秒后再次检查...")
            time.sleep(check_interval)
            
        except Exception as e:
            print(f"监控过程中出错: {e}")
            break

def main():
    """主程序"""
    try:
        print("=" * 60)
        print("OZON 商品状态查询工具")
        print("=" * 60)
        
        # 验证配置
        config.validate_config()
        
        # 初始化API客户端
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        if not api_client.test_api_connection():
            print("API连接失败，请检查配置")
            return
        
        print("API连接成功\n")
        
        # 获取用户输入
        while True:
            try:
                task_id_input = input("请输入任务ID (或输入 'q' 退出): ").strip()
                
                if task_id_input.lower() == 'q':
                    print("退出程序")
                    break
                
                task_id = int(task_id_input)
                
                # 选择操作模式
                print("\n请选择操作模式:")
                print("1. 查询当前状态")
                print("2. 监控状态变化")
                
                mode = input("请选择 (1 或 2): ").strip()
                
                if mode == "1":
                    check_single_task_status(api_client, task_id)
                elif mode == "2":
                    wait_minutes = input("最大等待时间 (分钟，默认10): ").strip()
                    try:
                        wait_minutes = int(wait_minutes) if wait_minutes else 10
                    except ValueError:
                        wait_minutes = 10
                    
                    monitor_task_status(api_client, task_id, wait_minutes)
                else:
                    print("无效选择，请重新输入")
                    continue
                
                print("\n" + "=" * 60)
                
            except ValueError:
                print("请输入有效的任务ID数字")
            except KeyboardInterrupt:
                print("\n用户中断操作")
                break
            except Exception as e:
                print(f"操作失败: {e}")
    
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
