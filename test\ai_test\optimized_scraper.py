#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的Ozon抓取器 - 基于测试结果的最佳实践
"""

import sys
import os
import time
import json
import random
from typing import Dict, List, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from curl_cffi import requests  # 使用 curl_cffi 替代标准 requests 以绕过反爬

class OptimizedOzonScraper:
    """优化的Ozon抓取器"""
    
    def __init__(self):
        # 最佳浏览器模拟选项（基于测试结果）
        self.best_impersonations = ["chrome104", "chrome110", "chrome99", "edge99"]
        
        # 必需的Cookie（从成功的抓包请求中提取）
        self.required_cookies = {
            "__Secure-ab-group": "69",
            "__Secure-user-id": "*********", 
            "bacntid": "5696816",
            "sc_company_id": "2977542",
            "__Secure-ext_xcid": "f23884cb255525641dd6d1d40f14a0a3",
            "cf_clearance": "SxNHjbiUgRHM7.bAwn6UymW.9nXAj7TGfxUe.epgtaY-1753754065-*******-kVN5.Nmbp2Zdl7S9kU_gVjFopKLumsA.NulKhlayK0ixUjTNsZftVSyKI7MCg3JjFV5i.EuYOJxhstBFrymL4tYaIjL7m2vj_bOY4_sSOuahhwxNGhbBVxHtF6UFiHOeGDvR.1d4EdE6PAjUUsPCc_uBKOP08yIpcOODIK3tkZ4qyLTa.2AtEw93MiYX2o5RmybaHDms4Y4_nGrVdte2sIwSgawPsGb0OkCa3No4wW4",
            "xcid": "2e10519482eb460cccc95029b438c85e",
            "rfuid": "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",
            "__Secure-ETC": "b8a49cd83eed5e85b03a09a0483af021",
            "abt_data": "7.4levO281ub12HHVswDD3B49mj9ujBtxMpPYdAzcSiLJttcuk4bTs7dQytIqB643JazLR9NK8PosNNkU-xMO-YMTpI0CH-l4SnblGJDzxj6dE21hhGd-WZQ0I-tLFGYV2znn9aY_OaVY-jsjzM13uC8P7r7-LUXZjoyCdXp-ZPmvY3kDkkhOPiQtfQqToO0wJl7jE9xvPBgogVJ0UUGVZD9gBNVORIqOFygtQf6q4VYWB2YdTTd2GJZEPf0WXJkMZr0c8U7wHTSHpHmWOaZlP5j1uAq3V2ELHctMZpCs_FeAl9OBl-OP2HFQFuBx2YLIGp8J0tmDjrNd_tuYfKIhOrsCutr4nWjukeXWOC62mWAb1u9Rp2aZz6_X97Krc6i0gYMgrpkCtVMT8Bwk38kjDdJleMBxxhRRBVoqkX8eJhc6UWHz4rkWa1Ea6FP6P-l6uOYuhBYA7zeXHYpVeD3qp0QUX6v-0ogcY-6KW_6oay8Mqcs-I5oQATwNBmEziWaNqjBIaZ1jWGE2wukXB6Qx_2b3TBboQ93k14tCn-potckyqDFwr72k0hDPYvhRP4dDjvWdxVeLvLWqat3WhEwkTvZATbC4IdzuUnSx4FcjbXdXeYc7ThFL6sxCRhbVU4WLNTxAaiL9fZ2JDoqV6",
            "__Secure-access-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.Fq0rJlpjcr9BI2UMH-vxr6m4rRreW1AWX6rtME6Q24w.1329ce1506708804a",
            "__Secure-refresh-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.ucJ59IaYF-vy4GJOPXVPkCdX1ZOeMOEHIsNXxh6bsIE.1ce4adb5ce4c55b7d"
        }
        
        # 推荐的请求头（可选，但建议使用以获得最佳效果）
        self.recommended_headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.3351.109',
            'sec-ch-ua-full-version-list': '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.169", "Microsoft Edge";v="138.0.3351.109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-o3-app-name': 'dweb_client',
            'x-o3-app-version': 'release_30-6-2025_551b2394',
            'x-o3-manifest-version': 'frontend-ozon-ru:551b23941a9d92fb35bd2c7850fae943cee48228,sf-render-api:100a986a9257dfdf22e7c029f43488da3e00e497',
            'x-o3-parent-requestid': '53f00ae1cd7aebb49bff904ecfb0b03d',
            'x-page-view-id': '56537c5d-1942-47eb-7dfc-a582686a7bb1'
        }

    def make_request(self, url: str, max_retries: int = 3, use_headers: bool = True) -> Optional[Dict[str, Any]]:
        """发送优化的请求"""
        
        for attempt in range(max_retries):
            # 随机选择最佳的浏览器模拟
            impersonation = random.choice(self.best_impersonations)
            
            # 准备请求头
            headers = self.recommended_headers.copy() if use_headers else {}
            if 'referer' not in headers and use_headers:
                headers['referer'] = url
            
            try:
                print(f"尝试 {attempt + 1}/{max_retries}: 使用 {impersonation} 模拟")
                
                response = requests.get(
                    url,
                    cookies=self.required_cookies,
                    headers=headers,
                    timeout=15,
                    impersonate=impersonation,
                    verify=False,
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    print(f"✅ 成功! 响应大小: {len(response.content)} bytes")
                    
                    try:
                        return response.json()
                    except:
                        # 如果不是JSON，返回文本内容
                        return {"content": response.text, "status": "success"}
                        
                else:
                    print(f"❌ HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求失败: {e}")
                
            # 如果不是最后一次尝试，等待一下再重试
            if attempt < max_retries - 1:
                wait_time = random.uniform(1, 3)
                print(f"等待 {wait_time:.1f}s 后重试...")
                time.sleep(wait_time)
        
        print(f"❌ 所有尝试都失败了")
        return None

    def scrape_product_api(self, product_url: str) -> Optional[Dict[str, Any]]:
        """抓取商品API数据"""
        # 构建API URL
        api_url = f"https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url={product_url}"
        
        print(f"抓取商品API: {product_url}")
        return self.make_request(api_url)

    def scrape_product_page(self, product_url: str) -> Optional[Dict[str, Any]]:
        """抓取商品页面HTML"""
        print(f"抓取商品页面: {product_url}")
        return self.make_request(product_url)

    def test_connection(self) -> bool:
        """测试连接是否正常"""
        test_url = "https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=https%3A%2F%2Fozon.ru%2Fnotification%2Fcookies_acceptance%3Fref_page_type%3Dpdp"
        
        print("测试连接...")
        result = self.make_request(test_url, max_retries=1)
        
        if result:
            print("✅ 连接测试成功!")
            return True
        else:
            print("❌ 连接测试失败!")
            return False

def main():
    """主程序 - 演示用法"""
    scraper = OptimizedOzonScraper()
    
    print("=" * 60)
    print("优化的Ozon抓取器")
    print("=" * 60)
    
    # 测试连接
    if not scraper.test_connection():
        print("连接测试失败，请检查网络或Cookie是否有效")
        return
    
    # 示例：抓取商品数据
    test_product_url = "https://www.ozon.ru/product/nabor-stolovoy-posudy-shiping-iz-10-predm-kolichestvo-person-10-1727409461/"
    
    print(f"\n测试抓取商品: {test_product_url}")
    
    # 方法1：抓取API数据
    print("\n方法1: 抓取API数据")
    api_data = scraper.scrape_product_api(test_product_url)
    if api_data:
        print(f"✅ API数据抓取成功，数据大小: {len(str(api_data))} 字符")
    else:
        print("❌ API数据抓取失败")
    
    # 方法2：抓取页面HTML
    print("\n方法2: 抓取页面HTML")
    page_data = scraper.scrape_product_page(test_product_url)
    if page_data:
        print(f"✅ 页面数据抓取成功，数据大小: {len(str(page_data))} 字符")
    else:
        print("❌ 页面数据抓取失败")

if __name__ == "__main__":
    main()
