#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie分析工具 - 专门分析Ozon Cookie的作用和重要性
"""

import sys
import os
import time
import json
import base64
import urllib.parse
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class CookieAnalyzer:
    """Cookie分析器"""
    
    def __init__(self):
        # 从成功请求中提取的Cookie
        self.cookies = {
            "__Secure-ab-group": "69",
            "__Secure-user-id": "*********", 
            "bacntid": "5696816",
            "sc_company_id": "2977542",
            "__Secure-ext_xcid": "f23884cb255525641dd6d1d40f14a0a3",
            "cf_clearance": "SxNHjbiUgRHM7.bAwn6UymW.9nXAj7TGfxUe.epgtaY-1753754065-*******-kVN5.Nmbp2Zdl7S9kU_gVjFopKLumsA.NulKhlayK0ixUjTNsZftVSyKI7MCg3JjFV5i.EuYOJxhstBFrymL4tYaIjL7m2vj_bOY4_sSOuahhwxNGhbBVxHtF6UFiHOeGDvR.1d4EdE6PAjUUsPCc_uBKOP08yIpcOODIK3tkZ4qyLTa.2AtEw93MiYX2o5RmybaHDms4Y4_nGrVdte2sIwSgawPsGb0OkCa3No4wW4",
            "xcid": "2e10519482eb460cccc95029b438c85e",
            "rfuid": "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",
            "__Secure-ETC": "b8a49cd83eed5e85b03a09a0483af021",
            "abt_data": "7.4levO281ub12HHVswDD3B49mj9ujBtxMpPYdAzcSiLJttcuk4bTs7dQytIqB643JazLR9NK8PosNNkU-xMO-YMTpI0CH-l4SnblGJDzxj6dE21hhGd-WZQ0I-tLFGYV2znn9aY_OaVY-jsjzM13uC8P7r7-LUXZjoyCdXp-ZPmvY3kDkkhOPiQtfQqToO0wJl7jE9xvPBgogVJ0UUGVZD9gBNVORIqOFygtQf6q4VYWB2YdTTd2GJZEPf0WXJkMZr0c8U7wHTSHpHmWOaZlP5j1uAq3V2ELHctMZpCs_FeAl9OBl-OP2HFQFuBx2YLIGp8J0tmDjrNd_tuYfKIhOrsCutr4nWjukeXWOC62mWAb1u9Rp2aZz6_X97Krc6i0gYMgrpkCtVMT8Bwk38kjDdJleMBxxhRRBVoqkX8eJhc6UWHz4rkWa1Ea6FP6P-l6uOYuhBYA7zeXHYpVeD3qp0QUX6v-0ogcY-6KW_6oay8Mqcs-I5oQATwNBmEziWaNqjBIaZ1jWGE2wukXB6Qx_2b3TBboQ93k14tCn-potckyqDFwr72k0hDPYvhRP4dDjvWdxVeLvLWqat3WhEwkTvZATbC4IdzuUnSx4FcjbXdXeYc7ThFL6sxCRhbVU4WLNTxAaiL9fZ2JDoqV6",
            "__Secure-access-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.Fq0rJlpjcr9BI2UMH-vxr6m4rRreW1AWX6rtME6Q24w.1329ce1506708804a",
            "__Secure-refresh-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.ucJ59IaYF-vy4GJOPXVPkCdX1ZOeMOEHIsNXxh6bsIE.1ce4adb5ce4c55b7d"
        }

    def analyze_cookie_structure(self, cookie_name: str, cookie_value: str) -> Dict[str, Any]:
        """分析单个Cookie的结构"""
        analysis = {
            "name": cookie_name,
            "value": cookie_value,
            "length": len(cookie_value),
            "type": "unknown",
            "components": {},
            "decoded_info": {},
            "security_level": "normal"
        }
        
        # 安全Cookie检测
        if cookie_name.startswith("__Secure-"):
            analysis["security_level"] = "secure"
        
        # 分析不同类型的Cookie
        if cookie_name == "__Secure-user-id":
            analysis["type"] = "user_identifier"
            analysis["components"]["user_id"] = cookie_value
            
        elif cookie_name == "__Secure-ab-group":
            analysis["type"] = "ab_testing"
            analysis["components"]["group_id"] = cookie_value
            
        elif cookie_name in ["__Secure-access-token", "__Secure-refresh-token"]:
            analysis["type"] = "authentication_token"
            # 尝试解析JWT格式的token
            parts = cookie_value.split('.')
            if len(parts) >= 3:
                analysis["components"]["token_parts"] = len(parts)
                analysis["components"]["user_id"] = parts[1] if len(parts) > 1 else None
                
        elif cookie_name == "cf_clearance":
            analysis["type"] = "cloudflare_clearance"
            # Cloudflare clearance token格式分析
            parts = cookie_value.split('-')
            if len(parts) >= 2:
                analysis["components"]["timestamp"] = parts[1] if len(parts) > 1 else None
                analysis["components"]["version"] = parts[2] if len(parts) > 2 else None
                
        elif cookie_name == "abt_data":
            analysis["type"] = "ab_testing_data"
            # 尝试Base64解码
            try:
                # abt_data通常是Base64编码的数据
                if len(cookie_value) > 100:  # 长度足够才尝试解码
                    analysis["components"]["encoded_length"] = len(cookie_value)
                    analysis["components"]["likely_base64"] = True
            except:
                pass
                
        elif cookie_name == "rfuid":
            analysis["type"] = "fingerprint_data"
            # rfuid包含设备指纹信息，通常是Base64编码
            try:
                decoded = base64.b64decode(cookie_value + "==")  # 添加padding
                analysis["decoded_info"]["fingerprint_data"] = "contains_device_info"
            except:
                pass
                
        elif cookie_name == "xcid":
            analysis["type"] = "session_identifier"
            analysis["components"]["session_id"] = cookie_value
            
        elif cookie_name == "__Secure-ext_xcid":
            analysis["type"] = "external_session_id"
            analysis["components"]["external_session"] = cookie_value
            
        elif cookie_name == "__Secure-ETC":
            analysis["type"] = "encrypted_tracking_cookie"
            analysis["components"]["tracking_hash"] = cookie_value
            
        return analysis

    def analyze_all_cookies(self):
        """分析所有Cookie"""
        print("=" * 80)
        print("Cookie详细分析报告")
        print("=" * 80)
        
        analyses = []
        for cookie_name, cookie_value in self.cookies.items():
            analysis = self.analyze_cookie_structure(cookie_name, cookie_value)
            analyses.append(analysis)
            
            print(f"\n🍪 Cookie: {cookie_name}")
            print(f"   类型: {analysis['type']}")
            print(f"   长度: {analysis['length']} 字符")
            print(f"   安全级别: {analysis['security_level']}")
            
            if analysis['components']:
                print("   组件:")
                for key, value in analysis['components'].items():
                    print(f"     - {key}: {value}")
            
            if analysis['decoded_info']:
                print("   解码信息:")
                for key, value in analysis['decoded_info'].items():
                    print(f"     - {key}: {value}")
        
        # 生成分类统计
        print("\n" + "=" * 80)
        print("Cookie分类统计")
        print("=" * 80)
        
        type_counts = {}
        security_counts = {}
        
        for analysis in analyses:
            cookie_type = analysis['type']
            security_level = analysis['security_level']
            
            type_counts[cookie_type] = type_counts.get(cookie_type, 0) + 1
            security_counts[security_level] = security_counts.get(security_level, 0) + 1
        
        print("按类型分类:")
        for cookie_type, count in sorted(type_counts.items()):
            print(f"  - {cookie_type}: {count} 个")
        
        print("\n按安全级别分类:")
        for security_level, count in sorted(security_counts.items()):
            print(f"  - {security_level}: {count} 个")
        
        return analyses

    def generate_cookie_importance_ranking(self):
        """生成Cookie重要性排名"""
        print("\n" + "=" * 80)
        print("Cookie重要性排名（基于类型和安全级别）")
        print("=" * 80)
        
        # 重要性评分规则
        importance_scores = {
            "authentication_token": 10,
            "user_identifier": 9,
            "cloudflare_clearance": 8,
            "ab_testing_data": 7,
            "fingerprint_data": 6,
            "session_identifier": 5,
            "external_session_id": 4,
            "encrypted_tracking_cookie": 3,
            "ab_testing": 2,
            "unknown": 1
        }
        
        security_bonus = {
            "secure": 2,
            "normal": 0
        }
        
        cookie_rankings = []
        for cookie_name, cookie_value in self.cookies.items():
            analysis = self.analyze_cookie_structure(cookie_name, cookie_value)
            
            base_score = importance_scores.get(analysis['type'], 1)
            security_score = security_bonus.get(analysis['security_level'], 0)
            total_score = base_score + security_score
            
            cookie_rankings.append({
                "name": cookie_name,
                "type": analysis['type'],
                "security_level": analysis['security_level'],
                "score": total_score,
                "length": analysis['length']
            })
        
        # 按分数排序
        cookie_rankings.sort(key=lambda x: x['score'], reverse=True)
        
        print("排名 | Cookie名称 | 类型 | 安全级别 | 分数 | 长度")
        print("-" * 80)
        
        for i, cookie in enumerate(cookie_rankings, 1):
            print(f"{i:2d}   | {cookie['name']:<25} | {cookie['type']:<20} | {cookie['security_level']:<8} | {cookie['score']:2d}   | {cookie['length']:4d}")
        
        return cookie_rankings

    def suggest_minimal_cookie_set(self):
        """建议最小Cookie集合"""
        print("\n" + "=" * 80)
        print("最小Cookie集合建议")
        print("=" * 80)
        
        rankings = self.generate_cookie_importance_ranking()
        
        # 必需Cookie（分数>=8）
        essential_cookies = [c for c in rankings if c['score'] >= 8]
        # 重要Cookie（分数6-7）
        important_cookies = [c for c in rankings if 6 <= c['score'] < 8]
        # 可选Cookie（分数<6）
        optional_cookies = [c for c in rankings if c['score'] < 6]
        
        print("🔴 必需Cookie（推荐保留）:")
        for cookie in essential_cookies:
            print(f"  - {cookie['name']} ({cookie['type']})")
        
        print(f"\n🟡 重要Cookie（建议保留）:")
        for cookie in important_cookies:
            print(f"  - {cookie['name']} ({cookie['type']})")
        
        print(f"\n🟢 可选Cookie（可以移除）:")
        for cookie in optional_cookies:
            print(f"  - {cookie['name']} ({cookie['type']})")
        
        # 生成最小集合
        minimal_set = [c['name'] for c in essential_cookies + important_cookies]
        
        print(f"\n推荐的最小Cookie集合 ({len(minimal_set)} 个):")
        for cookie_name in minimal_set:
            print(f"  - {cookie_name}")
        
        return minimal_set

    def export_analysis_report(self):
        """导出分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"cookie_analysis_report_{timestamp}.json"
        
        analyses = []
        for cookie_name, cookie_value in self.cookies.items():
            analysis = self.analyze_cookie_structure(cookie_name, cookie_value)
            analyses.append(analysis)
        
        rankings = self.generate_cookie_importance_ranking()
        minimal_set = self.suggest_minimal_cookie_set()
        
        report = {
            "timestamp": timestamp,
            "total_cookies": len(self.cookies),
            "cookie_analyses": analyses,
            "importance_rankings": rankings,
            "minimal_cookie_set": minimal_set,
            "summary": {
                "essential_cookies": len([c for c in rankings if c['score'] >= 8]),
                "important_cookies": len([c for c in rankings if 6 <= c['score'] < 8]),
                "optional_cookies": len([c for c in rankings if c['score'] < 6])
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已导出到: {filename}")
        return filename

def main():
    """主程序"""
    analyzer = CookieAnalyzer()
    
    print("Cookie分析工具")
    print("1. 分析所有Cookie")
    print("2. 生成重要性排名")
    print("3. 建议最小Cookie集合")
    print("4. 导出完整分析报告")
    print("5. 执行全部分析")
    
    choice = input("请选择操作 (1-5): ").strip()
    
    if choice == "1":
        analyzer.analyze_all_cookies()
    elif choice == "2":
        analyzer.generate_cookie_importance_ranking()
    elif choice == "3":
        analyzer.suggest_minimal_cookie_set()
    elif choice == "4":
        analyzer.export_analysis_report()
    elif choice == "5":
        analyzer.analyze_all_cookies()
        analyzer.generate_cookie_importance_ranking()
        analyzer.suggest_minimal_cookie_set()
        analyzer.export_analysis_report()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
