#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复类目映射问题的程序
找到 description_category_id 对应的 category_id
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
from modules.logger import api_logger
import config

def find_category_mapping(api_client: OzonApiClient):
    """查找 description_category_id 对应的 category_id"""
    try:
        print("获取类目树...")
        category_tree = api_client.get_category_tree()
        
        if not category_tree:
            print("获取类目树失败")
            return None
        
        print(f"获取到 {len(category_tree)} 个顶级类目")
        
        # 我们知道的 description_category_id
        target_description_category_id = 53968796
        
        print(f"查找 description_category_id={target_description_category_id} 对应的 category_id...")
        
        def search_category_tree(categories, target_id):
            """递归搜索类目树"""
            for category in categories:
                # 检查当前类目
                desc_cat_id = category.get('description_category_id')
                cat_id = category.get('category_id')
                
                if desc_cat_id == target_id:
                    print(f"找到匹配的类目!")
                    print(f"  description_category_id: {desc_cat_id}")
                    print(f"  category_id: {cat_id}")
                    print(f"  title: {category.get('title', 'N/A')}")
                    return cat_id
                
                # 检查子类目
                children = category.get('children', [])
                if children:
                    result = search_category_tree(children, target_id)
                    if result:
                        return result
            
            return None
        
        category_id = search_category_tree(category_tree, target_description_category_id)
        
        if category_id:
            print(f"成功找到映射: description_category_id={target_description_category_id} -> category_id={category_id}")
            return category_id
        else:
            print(f"未找到 description_category_id={target_description_category_id} 对应的 category_id")
            
            # 尝试查找相关的类目
            print("查找可能相关的类目...")
            vacuum_categories = []
            
            def find_vacuum_categories(categories):
                for category in categories:
                    title = category.get('title', '').lower()
                    if 'пылесос' in title or 'vacuum' in title or 'аксессуар' in title:
                        vacuum_categories.append({
                            'title': category.get('title'),
                            'category_id': category.get('category_id'),
                            'description_category_id': category.get('description_category_id')
                        })
                    
                    children = category.get('children', [])
                    if children:
                        find_vacuum_categories(children)
            
            find_vacuum_categories(category_tree)
            
            if vacuum_categories:
                print(f"找到 {len(vacuum_categories)} 个可能相关的类目:")
                for i, cat in enumerate(vacuum_categories[:10], 1):
                    print(f"  {i}. {cat['title']}")
                    print(f"     category_id: {cat['category_id']}")
                    print(f"     description_category_id: {cat['description_category_id']}")
                    print()
            
            return None
        
    except Exception as e:
        print(f"查找类目映射失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_attribute_values_with_correct_category_id(api_client: OzonApiClient, category_id: int):
    """使用正确的 category_id 测试属性值获取"""
    try:
        print(f"测试使用 category_id={category_id} 获取属性值...")
        
        # 测试获取"Тип"属性的字典值
        type_attribute_id = 8229  # 从之前的日志中获取
        
        print(f"获取属性 {type_attribute_id} 的字典值...")
        attribute_values = api_client.get_attribute_values(
            category_id=category_id,
            attribute_id=type_attribute_id
        )
        
        if attribute_values:
            print(f"成功获取到 {len(attribute_values)} 个属性值")
            print("前10个值:")
            for i, value in enumerate(attribute_values[:10], 1):
                print(f"  {i}. {value.get('value')} (ID: {value.get('id')})")
            
            # 查找"Аксессуар для робота-пылесоса"
            target_value = "Аксессуар для робота-пылесоса"
            print(f"\n查找目标值: '{target_value}'")
            
            for value in attribute_values:
                if target_value.lower() in value.get('value', '').lower():
                    print(f"找到匹配值: '{value.get('value')}' (ID: {value.get('id')})")
                    return value.get('id')
            
            print("未找到完全匹配的值，显示所有包含'аксессуар'的值:")
            for value in attribute_values:
                if 'аксессуар' in value.get('value', '').lower():
                    print(f"  - {value.get('value')} (ID: {value.get('id')})")
        else:
            print("未获取到属性值")
            
    except Exception as e:
        print(f"测试属性值获取失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主程序"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 查找类目映射
        category_id = find_category_mapping(api_client)
        
        if category_id:
            # 测试属性值获取
            test_attribute_values_with_correct_category_id(api_client, category_id)
            
            print(f"\n修复建议:")
            print(f"在 main.py 的数据映射函数中，将:")
            print(f"  api_client.get_attribute_values(category_id, attr_id)")
            print(f"修改为:")
            print(f"  api_client.get_attribute_values({category_id}, attr_id)")
            print(f"或者创建一个映射函数来转换 description_category_id -> category_id")
        else:
            print(f"\n无法找到正确的 category_id 映射")
            print(f"建议使用一个通用的 category_id 进行测试")
        
        print(f"\n修复完成!")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
