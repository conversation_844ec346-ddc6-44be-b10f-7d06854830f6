#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器 - 统一的测试入口点
提供简单的命令行界面来运行各种测试
"""

import sys
import os
import subprocess
from datetime import datetime

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("           🧪 Ozon Seller Tree API 测试工具")
    print("              基于 curl_cffi.requests 的测试套件")
    print("=" * 70)

def print_menu():
    """打印菜单"""
    print("\n📋 可用的测试选项:")
    print("1. 快速测试 (quick)")
    print("2. 完整测试 (full)")
    print("3. 高级测试 - 基本场景 (advanced)")
    print("4. 高级测试 - 所有场景 (advanced-all)")
    print("5. 浏览器指纹测试 (fingerprints)")
    print("6. 运行所有测试 (all)")
    print("0. 退出")

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n🚀 {description}")
    print("-" * 50)
    
    try:
        # 记录开始时间
        start_time = datetime.now()
        
        # 运行命令
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=False,  # 直接显示输出
            text=True,
            cwd=os.path.dirname(os.path.abspath(__file__))
        )
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"\n⏱️ 执行时间: {duration:.2f} 秒")
        
        if result.returncode == 0:
            print("✅ 测试完成")
        else:
            print(f"❌ 测试失败 (退出码: {result.returncode})")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def run_quick_test():
    """运行快速测试"""
    return run_command(
        "python quick_seller_tree_test.py",
        "运行快速测试 - 基本 API 验证"
    )

def run_full_test():
    """运行完整测试"""
    return run_command(
        "python test_ozon_seller_tree_api.py",
        "运行完整测试 - 多种 SKU 和场景"
    )

def run_advanced_basic():
    """运行高级测试 - 基本场景"""
    return run_command(
        "python advanced_seller_tree_test.py basic",
        "运行高级测试 - 基本场景"
    )

def run_advanced_all():
    """运行高级测试 - 所有场景"""
    return run_command(
        "python advanced_seller_tree_test.py all",
        "运行高级测试 - 所有测试场景"
    )

def run_fingerprint_tests():
    """运行浏览器指纹测试"""
    return run_command(
        "python advanced_seller_tree_test.py fingerprints",
        "运行浏览器指纹测试 - 测试不同浏览器模拟"
    )

def run_all_tests():
    """运行所有测试"""
    print("\n🎯 运行完整测试套件")
    print("=" * 50)
    
    tests = [
        ("快速测试", run_quick_test),
        ("完整测试", run_full_test),
        ("高级测试 - 基本", run_advanced_basic),
        ("高级测试 - 所有场景", run_advanced_all),
        ("浏览器指纹测试", run_fingerprint_tests)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📍 开始: {test_name}")
        success = test_func()
        results.append((test_name, success))
        
        if not success:
            print(f"⚠️ {test_name} 失败，继续下一个测试...")
    
    # 显示总结
    print("\n" + "=" * 70)
    print("📊 测试套件总结")
    print("=" * 70)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    print(f"成功率: {(passed/total)*100:.1f}%")

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    try:
        import curl_cffi
        print("✅ curl_cffi 已安装")
    except ImportError:
        print("❌ curl_cffi 未安装")
        print("   请运行: pip install curl-cffi")
        return False
    
    # 检查测试文件是否存在
    test_files = [
        "quick_seller_tree_test.py",
        "test_ozon_seller_tree_api.py", 
        "advanced_seller_tree_test.py",
        "test_config.py"
    ]
    
    missing_files = []
    for file in test_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少测试文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有测试文件存在")
    return True

def interactive_mode():
    """交互模式"""
    print_banner()
    
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先解决依赖问题")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("\n请选择测试选项 (0-6): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1" or choice.lower() == "quick":
                run_quick_test()
            elif choice == "2" or choice.lower() == "full":
                run_full_test()
            elif choice == "3" or choice.lower() == "advanced":
                run_advanced_basic()
            elif choice == "4" or choice.lower() == "advanced-all":
                run_advanced_all()
            elif choice == "5" or choice.lower() == "fingerprints":
                run_fingerprint_tests()
            elif choice == "6" or choice.lower() == "all":
                run_all_tests()
            else:
                print("❌ 无效选择，请重试")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def command_line_mode():
    """命令行模式"""
    if len(sys.argv) < 2:
        print("❌ 请提供测试类型参数")
        print("用法: python run_tests.py [quick|full|advanced|advanced-all|fingerprints|all]")
        return
    
    command = sys.argv[1].lower()
    
    print_banner()
    
    if not check_dependencies():
        print("\n❌ 依赖检查失败")
        return
    
    if command == "quick":
        run_quick_test()
    elif command == "full":
        run_full_test()
    elif command == "advanced":
        run_advanced_basic()
    elif command == "advanced-all":
        run_advanced_all()
    elif command == "fingerprints":
        run_fingerprint_tests()
    elif command == "all":
        run_all_tests()
    else:
        print(f"❌ 未知命令: {command}")
        print("可用命令: quick, full, advanced, advanced-all, fingerprints, all")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        command_line_mode()
    else:
        interactive_mode()

if __name__ == "__main__":
    main()
