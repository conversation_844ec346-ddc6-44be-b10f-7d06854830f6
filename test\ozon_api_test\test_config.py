#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件
包含 Ozon Seller Tree API 测试的所有配置参数
"""

# API 配置
API_CONFIG = {
    "base_url": "https://seller.ozon.ru",
    "seller_tree_endpoint": "/api/v1/seller-tree/resolve/by-sku",
    "timeout": 30,
    "impersonate": "chrome110"  # curl_cffi 浏览器指纹模拟
}

# 请求头配置
DEFAULT_HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'zh-Hans',
    'content-type': 'application/json',
    'origin': 'https://seller.ozon.ru',
    'priority': 'u=1, i',
    'referer': 'https://seller.ozon.ru/app/products/add/general-info',
    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"Windows"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
    'x-o3-app-name': 'seller-ui',
    'x-o3-company-id': '2977542',
    'x-o3-language': 'zh-Hans',
    'x-o3-page-type': 'products-other'
}

# Cookie 配置（从 curl 命令提取）
OZON_COOKIE = '__Secure-ab-group=72; __Secure-ext_xcid=27ca9db73a2885b37934c4130606a414; sc_company_id=2977542; __Secure-user-id=*********; bacntid=5696816; xcid=3b14ce650cfc9be64745fdb9568f961b; __Secure-access-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.yE60b99xGsn2J2f3HABVAblLp7spsUv9N-yLpw7RWPI.13bf208a7ad6f9e10; __Secure-refresh-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.FMm6Tul80XcJPDGioCF3qI2s3B-cb3klHs5zB8qsTa8.1e6b86f6a6dbe269c; x-o3-language=zh-Hans; rfuid=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Secure-ETC=a79631568bd4d9011cf502138c89ec98; abt_data=7._SMbImnoivov7DkMARlIu1R2yi5a-UIVlM4XDvkxHAweKYYVA-037SwT7EkbUVxtdpH2Idmbeah5Ml7dz3gMCtQUy3Qal00h1HmI-XxCOGbrqgQUF4Cpf21l8DjdGNbiUAU3xqbXEaG-5eJ0vnc71Na2T1vZtA7WKb3brekO0xJ2UmU6pINA_hwAhWIfYr_A5jGVwCdpG7XGkQPyzgS5mqAwDtaqylcgk_sDXwI9GAJT2QpdlMkoy5wUJYQ1Sa_HlltbpRvOZezrdWnbebN4brjzvis0r8TgajipH3GIFqx6kHsFpDJj0MqnXMvPT9SnuhEOE5bZIC3hVysKGJlxbGvDiyIzfS9fznxG5m5uAWSIpGaKRADrvdC_HfFFhdnM4z2jEqWxlZtSEz7fPtFkkJ4z8czxXVTDSSqBcUJtq6CsVHoC852t57ZQ98WSW0GOjV_w2GBXgUBatW_LRQiB5R9tA6_LxHhBYh89MZYVjaSqkVsZUcq5H1BpHmHGO4earXXBYS19DNH4BABf-Wk6wxA-pqy1xH_xczl_YH8TTIPffs44yC8Y0QNXR1gv69G0iksQyRtcP_lrTwUSrR8kT2a1VyX8ZBElCL7RAp2-srJifjqC5okfuXvOnas6RIycw9OL1sJ-x9JzhTv9-6Y'

# 测试数据配置
TEST_SKUS = {
    "original": ["2045588660"],  # 原始 curl 命令中的 SKU
    "test_single": ["1234567890"],  # 测试用的单个 SKU
    "test_multiple": ["2045588660", "1234567890", "9876543210"],  # 多个 SKU
    "empty": [],  # 空 SKU 列表
    "invalid": ["invalid_sku", ""],  # 无效 SKU
}

# 测试场景配置
TEST_SCENARIOS = {
    "basic": {
        "description": "基本请求测试",
        "skus": TEST_SKUS["original"],
        "expected_status": [200, 400, 401, 403]  # 可能的状态码
    },
    "multiple_skus": {
        "description": "多个 SKU 测试",
        "skus": TEST_SKUS["test_multiple"],
        "expected_status": [200, 400, 401, 403]
    },
    "empty_skus": {
        "description": "空 SKU 列表测试",
        "skus": TEST_SKUS["empty"],
        "expected_status": [400, 422]  # 预期会返回错误
    },
    "invalid_skus": {
        "description": "无效 SKU 测试",
        "skus": TEST_SKUS["invalid"],
        "expected_status": [200, 400, 404]
    }
}

# 浏览器指纹配置
BROWSER_FINGERPRINTS = [
    "chrome110",
    "chrome99",
    "edge99",
    "safari15_5"
]

# 输出配置
OUTPUT_CONFIG = {
    "save_results": True,
    "results_dir": "results",
    "log_level": "INFO",
    "pretty_print": True
}

def get_full_url():
    """获取完整的 API URL"""
    return API_CONFIG["base_url"] + API_CONFIG["seller_tree_endpoint"]

def get_headers_with_cookie():
    """获取包含 Cookie 的完整请求头"""
    headers = DEFAULT_HEADERS.copy()
    headers['Cookie'] = OZON_COOKIE
    return headers

def get_test_payload(scenario_name):
    """根据测试场景名称获取测试数据"""
    if scenario_name in TEST_SCENARIOS:
        return {"skus": TEST_SCENARIOS[scenario_name]["skus"]}
    return {"skus": TEST_SKUS["original"]}

# 使用示例
if __name__ == "__main__":
    print("测试配置信息:")
    print(f"API URL: {get_full_url()}")
    print(f"默认 SKUs: {TEST_SKUS['original']}")
    print(f"可用测试场景: {list(TEST_SCENARIOS.keys())}")
    print(f"浏览器指纹选项: {BROWSER_FINGERPRINTS}")
