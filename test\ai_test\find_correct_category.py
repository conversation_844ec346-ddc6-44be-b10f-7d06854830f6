#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找正确的类目ID映射
"""

import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
import config

def find_category_mapping(api_client: OzonApiClient):
    """查找 description_category_id 对应的正确 category_id"""
    try:
        print("获取类目树...")
        category_tree = api_client.get_category_tree()
        
        if not category_tree:
            print("获取类目树失败")
            return None
        
        print(f"获取到 {len(category_tree)} 个顶级类目")
        
        # 我们知道的 description_category_id
        target_description_category_id = 53968796
        
        print(f"查找 description_category_id={target_description_category_id} 对应的 category_id...")
        
        def search_category_tree(categories, target_id, path=""):
            """递归搜索类目树"""
            for category in categories:
                current_path = f"{path} > {category.get('title', 'Unknown')}" if path else category.get('title', 'Unknown')
                
                # 检查当前类目
                desc_cat_id = category.get('description_category_id')
                cat_id = category.get('category_id')
                
                # 打印类目信息以便调试
                if desc_cat_id == target_id:
                    print(f"找到匹配的类目!")
                    print(f"  路径: {current_path}")
                    print(f"  description_category_id: {desc_cat_id}")
                    print(f"  category_id: {cat_id}")
                    print(f"  title: {category.get('title', 'N/A')}")
                    print(f"  完整类目信息:")
                    for key, value in category.items():
                        if key != 'children':
                            print(f"    {key}: {value}")

                    # 如果当前类目没有 category_id，查找子类目
                    if not cat_id:
                        print(f"  当前类目没有 category_id，查找子类目...")
                        children = category.get('children', [])
                        if children:
                            print(f"  找到 {len(children)} 个子类目:")
                            for i, child in enumerate(children[:10], 1):  # 只显示前10个
                                child_cat_id = child.get('category_id')
                                child_desc_cat_id = child.get('description_category_id')
                                child_type_id = child.get('type_id')
                                child_name = child.get('category_name', child.get('type_name', 'Unknown'))

                                print(f"    {i}. {child_name}")
                                print(f"       category_id: {child_cat_id}")
                                print(f"       description_category_id: {child_desc_cat_id}")
                                print(f"       type_id: {child_type_id}")

                                # 如果找到有效的子类目，返回它
                                if child_cat_id and child_desc_cat_id:
                                    print(f"  使用子类目: {child_name}")
                                    return child_cat_id, child_desc_cat_id, child_type_id
                        else:
                            print(f"  没有子类目")

                    return cat_id
                
                # 检查子类目
                children = category.get('children', [])
                if children:
                    result = search_category_tree(children, target_id, current_path)
                    if result:
                        return result
            
            return None
        
        category_id = search_category_tree(category_tree, target_description_category_id)
        
        if category_id:
            print(f"\n成功找到映射:")
            print(f"description_category_id={target_description_category_id} -> category_id={category_id}")
            return category_id
        else:
            print(f"\n未找到 description_category_id={target_description_category_id} 对应的 category_id")
            
            # 尝试查找相关的类目
            print("\n查找可能相关的类目...")
            vacuum_categories = []
            
            def find_vacuum_categories(categories, path=""):
                for category in categories:
                    current_path = f"{path} > {category.get('title', 'Unknown')}" if path else category.get('title', 'Unknown')
                    title = category.get('title', '').lower()
                    
                    if any(keyword in title for keyword in ['пылесос', 'vacuum', 'аксессуар', 'робот']):
                        vacuum_categories.append({
                            'path': current_path,
                            'title': category.get('title'),
                            'category_id': category.get('category_id'),
                            'description_category_id': category.get('description_category_id'),
                            'type_id': category.get('type_id')
                        })
                    
                    children = category.get('children', [])
                    if children:
                        find_vacuum_categories(children, current_path)
            
            find_vacuum_categories(category_tree)
            
            if vacuum_categories:
                print(f"找到 {len(vacuum_categories)} 个可能相关的类目:")
                for i, cat in enumerate(vacuum_categories[:15], 1):  # 只显示前15个
                    print(f"\n{i}. {cat['title']}")
                    print(f"   路径: {cat['path']}")
                    print(f"   category_id: {cat['category_id']}")
                    print(f"   description_category_id: {cat['description_category_id']}")
                    print(f"   type_id: {cat['type_id']}")
                
                # 尝试找到一个合适的替代类目
                print(f"\n建议使用的类目:")
                for cat in vacuum_categories:
                    if cat['category_id'] and cat['description_category_id']:
                        print(f"类目: {cat['title']}")
                        print(f"  category_id: {cat['category_id']}")
                        print(f"  description_category_id: {cat['description_category_id']}")
                        print(f"  type_id: {cat['type_id']}")
                        return cat['category_id'], cat['description_category_id'], cat['type_id']
            
            return None
        
    except Exception as e:
        print(f"查找类目映射失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_category_attributes(api_client: OzonApiClient, category_id: int, type_id: int):
    """测试类目属性获取"""
    try:
        print(f"\n测试类目属性获取:")
        print(f"category_id: {category_id}")
        print(f"type_id: {type_id}")
        
        # 获取类目属性
        attributes = api_client.get_category_attributes(category_id, type_id)
        
        if attributes:
            required_attrs = [attr for attr in attributes if attr.get('is_required', False)]
            print(f"成功获取到 {len(attributes)} 个属性，其中 {len(required_attrs)} 个必填")
            
            print("必填属性:")
            for attr in required_attrs:
                print(f"  - {attr['name']} (ID: {attr['id']})")
            
            return True
        else:
            print("未获取到属性")
            return False
            
    except Exception as e:
        print(f"测试类目属性失败: {e}")
        return False

def main():
    """主程序"""
    try:
        print("=" * 60)
        print("查找正确的类目ID映射")
        print("=" * 60)
        
        # 验证配置
        config.validate_config()
        
        # 初始化API客户端
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        # 测试API连接
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 查找类目映射
        result = find_category_mapping(api_client)
        
        if result:
            if isinstance(result, tuple):
                category_id, description_category_id, type_id = result
                print(f"\n找到替代类目:")
                print(f"  category_id: {category_id}")
                print(f"  description_category_id: {description_category_id}")
                print(f"  type_id: {type_id}")
                
                # 测试属性获取
                if test_category_attributes(api_client, description_category_id, type_id):
                    print(f"\n修复建议:")
                    print(f"在 main.py 中修改:")
                    print(f"  category_id = {category_id}")
                    print(f"  description_category_id = {description_category_id}")
                    print(f"  type_id = {type_id}")
            else:
                category_id = result
                print(f"\n找到正确的 category_id: {category_id}")
        else:
            print(f"\n无法找到正确的类目映射")
        
        print(f"\n查找完成!")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
