#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ozon商品抓取策略测试工具
测试不同Cookie、请求头组合对抓取成功率的影响
"""

import sys
import os
import time
import json
import random
import urllib.parse
import warnings
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# 忽略 curl_cffi 的 __Secure- Cookie 警告
warnings.filterwarnings("ignore", message="`secure` changed to True for `__Secure-` prefixed cookies")

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
load_dotenv()

from curl_cffi import requests  # 使用 curl_cffi 替代标准 requests 以绕过反爬
from modules.logger import main_logger

class OzonScrapingTester:
    """Ozon抓取策略测试器"""

    def __init__(self):
        self.test_url = "https://www.ozon.ru/product/nabor-stolovoy-posudy-shiping-iz-10-predm-kolichestvo-person-10-1727409461/"
        self.product_id = "1727409461"
        self.api_url = f"https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=https%3A%2F%2Fozon.ru%2Fnotification%2Fcookies_acceptance%3Fref_page_type%3Dpdp"

        # 可用的浏览器模拟选项
        self.browser_impersonations = [
            "chrome110",
            "chrome107",
            "chrome104",
            "chrome101",
            "chrome99",
            "edge101",
            "edge99",
            "safari15_5",
            "safari15_3"
        ]
        
        # 从.env文件中读取Cookie
        self.full_cookies = self._load_cookies_from_env()

        # 基础请求头
        self.base_headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'referer': self.test_url,
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0'
        }
        
        # Ozon特定的请求头
        self.ozon_headers = {
            'x-o3-app-name': 'dweb_client',
            'x-o3-app-version': 'release_30-6-2025_551b2394',
            'x-o3-manifest-version': 'frontend-ozon-ru:551b23941a9d92fb35bd2c7850fae943cee48228,sf-render-api:100a986a9257dfdf22e7c029f43488da3e00e497',
            'x-o3-parent-requestid': '53f00ae1cd7aebb49bff904ecfb0b03d',
            'x-page-view-id': '56537c5d-1942-47eb-7dfc-a582686a7bb1'
        }
        
        # Chrome安全头
        self.chrome_headers = {
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.3351.109',
            'sec-ch-ua-full-version-list': '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.169", "Microsoft Edge";v="138.0.3351.109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0'
        }

    def _load_cookies_from_env(self) -> Dict[str, str]:
        """从.env文件中加载Cookie"""
        cookie_string = os.getenv("OZON_SESSION_COOKIE", "")

        if not cookie_string:
            print("警告: 未在.env文件中找到OZON_SESSION_COOKIE，使用空Cookie")
            return {}

        # 解析Cookie字符串为字典
        cookies = {}
        try:
            # 分割Cookie字符串
            cookie_pairs = cookie_string.split(';')
            for pair in cookie_pairs:
                if '=' in pair:
                    key, value = pair.strip().split('=', 1)
                    cookies[key.strip()] = value.strip()

            print(f"成功从.env文件加载了 {len(cookies)} 个Cookie")
            return cookies

        except Exception as e:
            print(f"解析Cookie字符串时出错: {e}")
            print("使用空Cookie继续执行")
            return {}

    def generate_cookie_combinations(self) -> List[Tuple[str, Dict[str, str]]]:
        """生成不同的Cookie组合进行测试"""
        combinations = []
        
        # 1. 完整Cookie
        combinations.append(("完整Cookie", self.full_cookies.copy()))
        
        # 2. 只保留认证相关的Cookie
        auth_cookies = {
            "__Secure-access-token": self.full_cookies["__Secure-access-token"],
            "__Secure-refresh-token": self.full_cookies["__Secure-refresh-token"],
            "__Secure-user-id": self.full_cookies["__Secure-user-id"]
        }
        combinations.append(("仅认证Cookie", auth_cookies))
        
        # 3. 只保留会话相关的Cookie
        session_cookies = {
            "abt_data": self.full_cookies["abt_data"],
            "xcid": self.full_cookies["xcid"],
            "__Secure-ETC": self.full_cookies["__Secure-ETC"]
        }
        combinations.append(("仅会话Cookie", session_cookies))
        
        # 4. 只保留安全相关的Cookie
        security_cookies = {
            "cf_clearance": self.full_cookies["cf_clearance"],
            "__Secure-ext_xcid": self.full_cookies["__Secure-ext_xcid"]
        }
        combinations.append(("仅安全Cookie", security_cookies))
        
        # 5. 最小化Cookie（只保留abt_data）
        minimal_cookies = {
            "abt_data": self.full_cookies["abt_data"]
        }
        combinations.append(("最小化Cookie", minimal_cookies))
        
        # 6. 无Cookie
        combinations.append(("无Cookie", {}))
        
        # 7. 核心Cookie组合
        core_cookies = {
            "abt_data": self.full_cookies["abt_data"],
            "cf_clearance": self.full_cookies["cf_clearance"],
            "__Secure-user-id": self.full_cookies["__Secure-user-id"]
        }
        combinations.append(("核心Cookie", core_cookies))
        
        return combinations

    def generate_header_combinations(self) -> List[Tuple[str, Dict[str, str]]]:
        """生成不同的请求头组合进行测试"""
        combinations = []
        
        # 1. 完整请求头
        full_headers = {}
        full_headers.update(self.base_headers)
        full_headers.update(self.ozon_headers)
        full_headers.update(self.chrome_headers)
        combinations.append(("完整请求头", full_headers))
        
        # 2. 基础请求头
        combinations.append(("基础请求头", self.base_headers.copy()))
        
        # 3. 基础 + Ozon特定请求头
        basic_ozon = {}
        basic_ozon.update(self.base_headers)
        basic_ozon.update(self.ozon_headers)
        combinations.append(("基础+Ozon请求头", basic_ozon))
        
        # 4. 基础 + Chrome安全请求头
        basic_chrome = {}
        basic_chrome.update(self.base_headers)
        basic_chrome.update(self.chrome_headers)
        combinations.append(("基础+Chrome请求头", basic_chrome))
        
        # 5. 最小化请求头
        minimal_headers = {
            'accept': 'application/json',
            'user-agent': self.base_headers['user-agent']
        }
        combinations.append(("最小化请求头", minimal_headers))
        
        # 6. 只有Ozon特定请求头
        combinations.append(("仅Ozon请求头", self.ozon_headers.copy()))
        
        return combinations

    def test_request(self, name: str, cookies: Dict[str, str], headers: Dict[str, str]) -> Dict:
        """测试单个请求组合"""
        result = {
            "name": name,
            "success": False,
            "status_code": None,
            "response_size": 0,
            "error": None,
            "response_time": 0,
            "has_product_data": False
        }

        try:
            start_time = time.time()

            # 使用 curl_cffi 发送请求，模拟真实浏览器
            response = requests.get(
                self.api_url,
                cookies=cookies,
                headers=headers,
                timeout=10,
                impersonate="chrome110",  # 模拟 Chrome 110 浏览器
                verify=False,  # 跳过SSL验证
                allow_redirects=True,
                proxies=None  # 可以在这里添加代理
            )

            result["response_time"] = time.time() - start_time
            result["status_code"] = response.status_code
            result["response_size"] = len(response.content)

            if response.status_code == 200:
                result["success"] = True

                # 检查响应是否包含商品数据
                try:
                    data = response.json()
                    if "result" in data and data["result"]:
                        result["has_product_data"] = True
                except:
                    pass
            else:
                result["error"] = f"HTTP {response.status_code}"

        except Exception as e:
            if "timeout" in str(e).lower():
                result["error"] = "请求超时"
            elif "connection" in str(e).lower():
                result["error"] = "连接错误"
            else:
                result["error"] = str(e)

        return result

    def run_comprehensive_test(self):
        """运行全面的测试"""
        print("=" * 80)
        print("Ozon商品抓取策略全面测试")
        print("=" * 80)
        
        cookie_combinations = self.generate_cookie_combinations()
        header_combinations = self.generate_header_combinations()
        
        all_results = []
        test_count = 0
        total_tests = len(cookie_combinations) * len(header_combinations)
        
        print(f"总共将进行 {total_tests} 个测试组合")
        print()
        
        for cookie_name, cookies in cookie_combinations:
            for header_name, headers in header_combinations:
                test_count += 1
                test_name = f"{cookie_name} + {header_name}"
                
                print(f"[{test_count}/{total_tests}] 测试: {test_name}")
                
                result = self.test_request(test_name, cookies, headers)
                all_results.append(result)
                
                # 显示结果
                status = "✅ 成功" if result["success"] else f"❌ 失败 ({result['error']})"
                print(f"  结果: {status}")
                print(f"  状态码: {result['status_code']}")
                print(f"  响应大小: {result['response_size']} bytes")
                print(f"  响应时间: {result['response_time']:.2f}s")
                print(f"  包含商品数据: {'是' if result['has_product_data'] else '否'}")
                print()
                
                # 避免请求过于频繁
                time.sleep(1)
        
        # 生成测试报告
        self.generate_report(all_results)

    def generate_report(self, results: List[Dict]):
        """生成测试报告"""
        print("=" * 80)
        print("测试报告")
        print("=" * 80)
        
        # 统计成功率
        total_tests = len(results)
        successful_tests = len([r for r in results if r["success"]])
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试数: {successful_tests}")
        print(f"成功率: {success_rate:.1f}%")
        print()
        
        # 成功的组合
        successful_combinations = [r for r in results if r["success"]]
        if successful_combinations:
            print("✅ 成功的组合:")
            for result in successful_combinations:
                print(f"  - {result['name']}")
                print(f"    状态码: {result['status_code']}, 响应大小: {result['response_size']} bytes")
                print(f"    响应时间: {result['response_time']:.2f}s, 包含商品数据: {'是' if result['has_product_data'] else '否'}")
            print()
        
        # 失败的组合
        failed_combinations = [r for r in results if not r["success"]]
        if failed_combinations:
            print("❌ 失败的组合:")
            for result in failed_combinations:
                print(f"  - {result['name']}: {result['error']}")
            print()
        
        # 错误类型统计
        error_types = {}
        for result in failed_combinations:
            error = result["error"] or "未知错误"
            error_types[error] = error_types.get(error, 0) + 1
        
        if error_types:
            print("错误类型统计:")
            for error, count in sorted(error_types.items(), key=lambda x: x[1], reverse=True):
                print(f"  - {error}: {count} 次")
            print()
        
        # 保存详细结果到文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ozon_scraping_test_results_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"详细测试结果已保存到: {filename}")

    def test_specific_combination(self, cookie_name: str, header_name: str):
        """测试特定的Cookie和请求头组合"""
        cookie_combinations = self.generate_cookie_combinations()
        header_combinations = self.generate_header_combinations()
        
        # 查找指定的组合
        cookies = None
        headers = None
        
        for name, cookie_dict in cookie_combinations:
            if name == cookie_name:
                cookies = cookie_dict
                break
        
        for name, header_dict in header_combinations:
            if name == header_name:
                headers = header_dict
                break
        
        if cookies is None:
            print(f"未找到Cookie组合: {cookie_name}")
            return
        
        if headers is None:
            print(f"未找到请求头组合: {header_name}")
            return
        
        print(f"测试组合: {cookie_name} + {header_name}")
        print("-" * 50)
        
        result = self.test_request(f"{cookie_name} + {header_name}", cookies, headers)
        
        print(f"结果: {'✅ 成功' if result['success'] else f'❌ 失败 ({result['error']})'}")
        print(f"状态码: {result['status_code']}")
        print(f"响应大小: {result['response_size']} bytes")
        print(f"响应时间: {result['response_time']:.2f}s")
        print(f"包含商品数据: {'是' if result['has_product_data'] else '否'}")

    def analyze_critical_cookies(self):
        """分析关键Cookie的影响"""
        print("=" * 80)
        print("关键Cookie影响分析")
        print("=" * 80)

        # 测试每个Cookie的重要性
        cookie_importance = {}
        base_headers = {}
        base_headers.update(self.base_headers)
        base_headers.update(self.ozon_headers)
        base_headers.update(self.chrome_headers)

        for cookie_name in self.full_cookies.keys():
            print(f"测试移除Cookie: {cookie_name}")

            # 创建不包含当前Cookie的Cookie集合
            test_cookies = self.full_cookies.copy()
            del test_cookies[cookie_name]

            result = self.test_request(f"无{cookie_name}", test_cookies, base_headers)
            cookie_importance[cookie_name] = result

            status = "✅ 成功" if result["success"] else f"❌ 失败 ({result['error']})"
            print(f"  结果: {status}")
            print()

            time.sleep(0.5)

        # 分析结果
        print("Cookie重要性分析:")
        critical_cookies = []
        for cookie_name, result in cookie_importance.items():
            if not result["success"]:
                critical_cookies.append(cookie_name)
                print(f"  🔴 关键Cookie: {cookie_name} - 移除后失败")
            else:
                print(f"  🟢 可选Cookie: {cookie_name} - 移除后仍成功")

        print(f"\n关键Cookie总数: {len(critical_cookies)}")
        if critical_cookies:
            print("关键Cookie列表:")
            for cookie in critical_cookies:
                print(f"  - {cookie}")

    def analyze_critical_headers(self):
        """分析关键请求头的影响"""
        print("=" * 80)
        print("关键请求头影响分析")
        print("=" * 80)

        # 测试每个请求头的重要性
        header_importance = {}
        full_headers = {}
        full_headers.update(self.base_headers)
        full_headers.update(self.ozon_headers)
        full_headers.update(self.chrome_headers)

        for header_name in full_headers.keys():
            print(f"测试移除请求头: {header_name}")

            # 创建不包含当前请求头的请求头集合
            test_headers = full_headers.copy()
            del test_headers[header_name]

            result = self.test_request(f"无{header_name}", self.full_cookies, test_headers)
            header_importance[header_name] = result

            status = "✅ 成功" if result["success"] else f"❌ 失败 ({result['error']})"
            print(f"  结果: {status}")
            print()

            time.sleep(0.5)

        # 分析结果
        print("请求头重要性分析:")
        critical_headers = []
        for header_name, result in header_importance.items():
            if not result["success"]:
                critical_headers.append(header_name)
                print(f"  🔴 关键请求头: {header_name} - 移除后失败")
            else:
                print(f"  🟢 可选请求头: {header_name} - 移除后仍成功")

        print(f"\n关键请求头总数: {len(critical_headers)}")
        if critical_headers:
            print("关键请求头列表:")
            for header in critical_headers:
                print(f"  - {header}")

    def find_minimal_working_combination(self):
        """寻找最小可工作组合"""
        print("=" * 80)
        print("寻找最小可工作组合")
        print("=" * 80)

        # 首先确定关键Cookie
        critical_cookies = {}
        for cookie_name, cookie_value in self.full_cookies.items():
            test_cookies = {cookie_name: cookie_value}
            result = self.test_request(f"仅{cookie_name}", test_cookies, self.base_headers)
            if result["success"]:
                critical_cookies[cookie_name] = cookie_value
                print(f"✅ {cookie_name} 单独可工作")
            else:
                print(f"❌ {cookie_name} 单独不可工作")

        print(f"\n单独可工作的Cookie: {len(critical_cookies)}")

        # 尝试最小组合
        if critical_cookies:
            print("\n测试最小Cookie组合...")
            result = self.test_request("最小Cookie组合", critical_cookies, self.base_headers)
            if result["success"]:
                print("✅ 最小Cookie组合可工作")
                print("最小可工作Cookie:")
                for cookie_name in critical_cookies:
                    print(f"  - {cookie_name}")
            else:
                print("❌ 最小Cookie组合不可工作，需要更多Cookie")

    def test_browser_impersonations(self):
        """测试不同的浏览器模拟"""
        print("=" * 80)
        print("浏览器模拟测试")
        print("=" * 80)

        # 使用完整的Cookie和请求头
        full_headers = {}
        full_headers.update(self.base_headers)
        full_headers.update(self.ozon_headers)
        full_headers.update(self.chrome_headers)

        results = []

        for impersonation in self.browser_impersonations:
            print(f"测试浏览器模拟: {impersonation}")

            try:
                start_time = time.time()

                response = requests.get(
                    self.api_url,
                    cookies=self.full_cookies,
                    headers=full_headers,
                    timeout=10,
                    impersonate=impersonation,
                    verify=False,
                    allow_redirects=True
                )

                response_time = time.time() - start_time

                result = {
                    "impersonation": impersonation,
                    "success": response.status_code == 200,
                    "status_code": response.status_code,
                    "response_size": len(response.content),
                    "response_time": response_time,
                    "error": None
                }

                if response.status_code == 200:
                    try:
                        data = response.json()
                        result["has_product_data"] = "result" in data and data["result"]
                    except:
                        result["has_product_data"] = False
                else:
                    result["error"] = f"HTTP {response.status_code}"

                results.append(result)

                status = "✅ 成功" if result["success"] else f"❌ 失败 ({result['error']})"
                print(f"  结果: {status}")
                print(f"  状态码: {result['status_code']}")
                print(f"  响应大小: {result['response_size']} bytes")
                print(f"  响应时间: {result['response_time']:.2f}s")
                print()

            except Exception as e:
                result = {
                    "impersonation": impersonation,
                    "success": False,
                    "status_code": None,
                    "response_size": 0,
                    "response_time": 0,
                    "error": str(e),
                    "has_product_data": False
                }
                results.append(result)

                print(f"  结果: ❌ 失败 ({str(e)})")
                print()

            time.sleep(1)  # 避免请求过于频繁

        # 生成报告
        print("浏览器模拟测试报告:")
        print("-" * 50)

        successful_impersonations = [r for r in results if r["success"]]
        failed_impersonations = [r for r in results if not r["success"]]

        print(f"成功的模拟: {len(successful_impersonations)}/{len(results)}")

        if successful_impersonations:
            print("\n✅ 成功的浏览器模拟:")
            for result in successful_impersonations:
                print(f"  - {result['impersonation']}: {result['response_size']} bytes, {result['response_time']:.2f}s")

        if failed_impersonations:
            print("\n❌ 失败的浏览器模拟:")
            for result in failed_impersonations:
                print(f"  - {result['impersonation']}: {result['error']}")

        return results

def main():
    """主程序"""
    tester = OzonScrapingTester()

    print("Ozon商品抓取策略测试工具")
    print("1. 运行全面测试")
    print("2. 测试特定组合")
    print("3. 显示可用组合")
    print("4. 分析关键Cookie")
    print("5. 分析关键请求头")
    print("6. 寻找最小可工作组合")
    print("7. 测试浏览器模拟")

    choice = input("请选择操作 (1-7): ").strip()

    if choice == "1":
        tester.run_comprehensive_test()
    elif choice == "2":
        print("\n可用的Cookie组合:")
        cookie_combinations = tester.generate_cookie_combinations()
        for i, (name, _) in enumerate(cookie_combinations, 1):
            print(f"  {i}. {name}")

        print("\n可用的请求头组合:")
        header_combinations = tester.generate_header_combinations()
        for i, (name, _) in enumerate(header_combinations, 1):
            print(f"  {i}. {name}")

        cookie_name = input("\n请输入Cookie组合名称: ").strip()
        header_name = input("请输入请求头组合名称: ").strip()

        tester.test_specific_combination(cookie_name, header_name)
    elif choice == "3":
        print("\n可用的Cookie组合:")
        cookie_combinations = tester.generate_cookie_combinations()
        for i, (name, cookies) in enumerate(cookie_combinations, 1):
            print(f"  {i}. {name} ({len(cookies)} 个Cookie)")

        print("\n可用的请求头组合:")
        header_combinations = tester.generate_header_combinations()
        for i, (name, headers) in enumerate(header_combinations, 1):
            print(f"  {i}. {name} ({len(headers)} 个请求头)")
    elif choice == "4":
        tester.analyze_critical_cookies()
    elif choice == "5":
        tester.analyze_critical_headers()
    elif choice == "6":
        tester.find_minimal_working_combination()
    elif choice == "7":
        tester.test_browser_impersonations()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
