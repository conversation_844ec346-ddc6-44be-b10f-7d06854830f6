#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 Ozon Seller Tree API 请求
基于提供的 curl 命令，使用 curl_cffi.requests 进行请求测试
API: https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku
"""

import json
import sys
import os
from curl_cffi import requests
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class OzonSellerTreeAPITest:
    """Ozon Seller Tree API 测试类"""
    
    def __init__(self):
        """初始化测试类"""
        self.api_url = "https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku"
        self.session = requests.Session()
        self.setup_headers()
    
    def setup_headers(self):
        """设置请求头"""
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-Hans',
            'content-type': 'application/json',
            'origin': 'https://seller.ozon.ru',
            'priority': 'u=1, i',
            'referer': 'https://seller.ozon.ru/app/products/add/general-info',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-o3-app-name': 'seller-ui',
            'x-o3-company-id': '2977542',
            'x-o3-language': 'zh-Hans',
            'x-o3-page-type': 'products-other'
        }
        
        # 设置 Cookie（从 curl 命令中提取）
        self.cookie = '__Secure-ab-group=72; __Secure-ext_xcid=27ca9db73a2885b37934c4130606a414; sc_company_id=2977542; __Secure-user-id=*********; bacntid=5696816; xcid=3b14ce650cfc9be64745fdb9568f961b; __Secure-access-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.yE60b99xGsn2J2f3HABVAblLp7spsUv9N-yLpw7RWPI.13bf208a7ad6f9e10; __Secure-refresh-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.FMm6Tul80XcJPDGioCF3qI2s3B-cb3klHs5zB8qsTa8.1e6b86f6a6dbe269c; x-o3-language=zh-Hans; rfuid=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Secure-ETC=a79631568bd4d9011cf502138c89ec98; abt_data=7._SMbImnoivov7DkMARlIu1R2yi5a-UIVlM4XDvkxHAweKYYVA-037SwT7EkbUVxtdpH2Idmbeah5Ml7dz3gMCtQUy3Qal00h1HmI-XxCOGbrqgQUF4Cpf21l8DjdGNbiUAU3xqbXEaG-5eJ0vnc71Na2T1vZtA7WKb3brekO0xJ2UmU6pINA_hwAhWIfYr_A5jGVwCdpG7XGkQPyzgS5mqAwDtaqylcgk_sDXwI9GAJT2QpdlMkoy5wUJYQ1Sa_HlltbpRvOZezrdWnbebN4brjzvis0r8TgajipH3GIFqx6kHsFpDJj0MqnXMvPT9SnuhEOE5bZIC3hVysKGJlxbGvDiyIzfS9fznxG5m5uAWSIpGaKRADrvdC_HfFFhdnM4z2jEqWxlZtSEz7fPtFkkJ4z8czxXVTDSSqBcUJtq6CsVHoC852t57ZQ98WSW0GOjV_w2GBXgUBatW_LRQiB5R9tA6_LxHhBYh89MZYVjaSqkVsZUcq5H1BpHmHGO4earXXBYS19DNH4BABf-Wk6wxA-pqy1xH_xczl_YH8TTIPffs44yC8Y0QNXR1gv69G0iksQyRtcP_lrTwUSrR8kT2a1VyX8ZBElCL7RAp2-srJifjqC5okfuXvOnas6RIycw9OL1sJ-x9JzhTv9-6Y'
        
        self.headers['Cookie'] = self.cookie
    
    def test_basic_request(self, skus=None):
        """
        测试基本的 API 请求
        
        Args:
            skus (list): SKU 列表，默认使用示例 SKU
        """
        if skus is None:
            skus = ["2045588660"]  # 使用 curl 命令中的示例 SKU
        
        payload = {"skus": skus}
        
        print(f"🔧 测试 Ozon Seller Tree API 请求...")
        print(f"   URL: {self.api_url}")
        print(f"   SKUs: {skus}")
        
        try:
            response = self.session.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=30,
                impersonate="chrome110",  # 使用 curl_cffi 的浏览器指纹模拟
                verify=True
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print("✅ 请求成功")
                try:
                    data = response.json()
                    print(f"   响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    return True, data
                except json.JSONDecodeError:
                    print(f"   响应内容 (非JSON): {response.text[:500]}...")
                    return True, response.text
            else:
                print(f"❌ 请求失败")
                print(f"   错误内容: {response.text[:500]}...")
                return False, response.text
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return False, str(e)
    
    def test_with_different_skus(self):
        """测试不同的 SKU"""
        test_skus = [
            ["2045588660"],  # 原始 SKU
            ["1234567890"],  # 测试 SKU
            ["2045588660", "1234567890"],  # 多个 SKU
        ]
        
        results = []
        for skus in test_skus:
            print(f"\n{'='*50}")
            success, data = self.test_basic_request(skus)
            results.append({
                'skus': skus,
                'success': success,
                'data': data
            })
        
        return results
    
    def test_headers_validation(self):
        """测试不同的请求头配置"""
        print(f"\n🔧 测试请求头配置...")
        
        # 测试最小请求头
        minimal_headers = {
            'accept': 'application/json',
            'content-type': 'application/json',
            'Cookie': self.cookie
        }
        
        try:
            response = self.session.post(
                self.api_url,
                headers=minimal_headers,
                json={"skus": ["2045588660"]},
                timeout=30,
                impersonate="chrome110",
                verify=True
            )
            
            print(f"   最小请求头测试 - 状态码: {response.status_code}")
            return response.status_code == 200
            
        except Exception as e:
            print(f"   最小请求头测试失败: {e}")
            return False
    
    def save_test_results(self, results):
        """保存测试结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test/ozon_seller_tree_test_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"📁 测试结果已保存到: {filename}")
        except Exception as e:
            print(f"❌ 保存测试结果失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("        Ozon Seller Tree API 测试")
    print("    使用 curl_cffi.requests 模拟浏览器请求")
    print("=" * 60)
    
    # 创建测试实例
    tester = OzonSellerTreeAPITest()
    
    # 执行测试
    all_results = {
        'timestamp': datetime.now().isoformat(),
        'api_url': tester.api_url,
        'tests': {}
    }
    
    # 1. 基本请求测试
    print("\n📋 1. 基本请求测试")
    success, data = tester.test_basic_request()
    all_results['tests']['basic_request'] = {
        'success': success,
        'data': data
    }
    
    # 2. 不同 SKU 测试
    print("\n📋 2. 不同 SKU 测试")
    sku_results = tester.test_with_different_skus()
    all_results['tests']['different_skus'] = sku_results
    
    # 3. 请求头验证测试
    print("\n📋 3. 请求头验证测试")
    headers_success = tester.test_headers_validation()
    all_results['tests']['headers_validation'] = {
        'success': headers_success
    }
    
    # 保存结果
    tester.save_test_results(all_results)
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   基本请求: {'✅ 成功' if success else '❌ 失败'}")
    print(f"   请求头验证: {'✅ 成功' if headers_success else '❌ 失败'}")
    print(f"   SKU 测试: {len([r for r in sku_results if r['success']])}/{len(sku_results)} 成功")
    print("=" * 60)

if __name__ == "__main__":
    main()
