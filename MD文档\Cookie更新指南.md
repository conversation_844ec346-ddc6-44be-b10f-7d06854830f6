# 🍪 Ozon Cookie 更新指南

## 为什么需要更新 Cookie？

当遇到 403 错误时，通常是因为：
1. **Cookie 过期**：认证令牌有时效性
2. **会话失效**：长时间未活动导致会话过期
3. **IP 变化**：Cookie 可能绑定特定 IP
4. **反爬检测**：频繁请求触发安全机制

## 🔧 如何获取新的 Cookie

### 步骤 1：打开浏览器
1. 使用 **Chrome** 或 **Edge** 浏览器
2. 访问 `https://seller.ozon.ru`
3. 正常登录您的 Ozon 卖家账户

### 步骤 2：打开开发者工具
1. 按 `F12` 或右键选择"检查"
2. 切换到 **Network（网络）** 标签页
3. 勾选 **Preserve log（保留日志）**

### 步骤 3：触发请求
1. 在 Ozon 卖家后台进行任意操作（如刷新页面）
2. 在网络标签页中找到任意请求
3. 点击请求，查看 **Request Headers（请求头）**

### 步骤 4：复制 Cookie
1. 找到 `Cookie:` 行
2. 复制完整的 Cookie 字符串
3. Cookie 通常很长，包含多个键值对

### 步骤 5：更新 .env 文件
```bash
# 打开 .env 文件
# 替换 OZON_SESSION_COOKIE 的值

OZON_SESSION_COOKIE="你的新Cookie字符串"
```

## 🎯 Cookie 示例格式

正确的 Cookie 应该包含这些关键部分：
```
__Secure-access-token=8.*********.xxx...
__Secure-user-id=*********
sc_company_id=2977542
xcid=xxx...
rfuid=xxx...
abt_data=7.xxx...
```

## ⚠️ 重要注意事项

### 安全提醒
- **不要分享 Cookie**：包含敏感的认证信息
- **定期更新**：建议每天更新一次
- **保护隐私**：不要在公共场所操作

### 有效性检查
- Cookie 中应包含 `__Secure-access-token`
- `__Secure-user-id` 应该是您的用户ID
- `sc_company_id` 应该是您的公司ID

### 常见问题
1. **Cookie 太短**：可能复制不完整
2. **包含换行**：应该是一行连续的字符串
3. **缺少关键字段**：确保包含所有认证相关的 Cookie

## 🔄 自动化更新建议

### 方法 1：浏览器书签
创建一个书签，点击即可复制 Cookie：
```javascript
javascript:(function(){
    var cookies = document.cookie;
    navigator.clipboard.writeText(cookies);
    alert('Cookie已复制到剪贴板');
})();
```

### 方法 2：浏览器扩展
使用 Cookie 管理扩展，如：
- Cookie Editor
- EditThisCookie
- Cookie Manager

## 🧪 验证新 Cookie

更新 Cookie 后，运行测试脚本验证：
```bash
python test_anti_bot_fix.py
```

如果仍然出现 403 错误，可能需要：
1. 重新登录 Ozon 账户
2. 清除浏览器缓存
3. 使用无痕模式获取 Cookie
4. 检查账户是否被限制

## 📞 故障排除

### 如果 Cookie 更新后仍然失败：

1. **检查账户状态**
   - 确认账户未被封禁
   - 验证登录状态正常

2. **网络环境**
   - 尝试更换网络
   - 使用 VPN（如果适用）

3. **请求频率**
   - 降低请求频率
   - 增加请求间隔

4. **浏览器环境**
   - 使用相同的浏览器和操作系统
   - 保持 User-Agent 一致

## 🔄 定期维护

建议建立定期维护流程：
1. **每日检查**：监控抓取成功率
2. **及时更新**：发现 403 错误立即更新 Cookie
3. **备份策略**：准备多个有效的 Cookie
4. **监控日志**：定期查看错误日志

通过遵循这个指南，您应该能够有效地维护 Cookie 的有效性，确保抓取工具的正常运行。
