[{"name": "完整Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 10542, "error": "HTTP 403", "response_time": 2.7294392585754395, "has_product_data": false}, {"name": "完整Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 10439, "error": "HTTP 403", "response_time": 0.8408491611480713, "has_product_data": false}, {"name": "完整Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 10542, "error": "HTTP 403", "response_time": 0.6385231018066406, "has_product_data": false}, {"name": "完整Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 10439, "error": "HTTP 403", "response_time": 0.7377917766571045, "has_product_data": false}, {"name": "完整Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 10239, "error": "HTTP 403", "response_time": 0.8431453704833984, "has_product_data": false}, {"name": "完整Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 10295, "error": "HTTP 403", "response_time": 0.779202938079834, "has_product_data": false}, {"name": "仅认证Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7298, "error": "HTTP 403", "response_time": 0.8004074096679688, "has_product_data": false}, {"name": "仅认证Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7223, "error": "HTTP 403", "response_time": 1.558598518371582, "has_product_data": false}, {"name": "仅认证<PERSON><PERSON> + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7298, "error": "HTTP 403", "response_time": 0.8422913551330566, "has_product_data": false}, {"name": "仅认证Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7251, "error": "HTTP 403", "response_time": 0.8418569564819336, "has_product_data": false}, {"name": "仅认证Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 7023, "error": "HTTP 403", "response_time": 0.8399684429168701, "has_product_data": false}, {"name": "仅认证<PERSON>ie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 7079, "error": "HTTP 403", "response_time": 0.8418891429901123, "has_product_data": false}, {"name": "仅会话Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7762, "error": "HTTP 403", "response_time": 0.7457249164581299, "has_product_data": false}, {"name": "仅会话Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7687, "error": "HTTP 403", "response_time": 0.6298553943634033, "has_product_data": false}, {"name": "仅会话<PERSON><PERSON> + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7762, "error": "HTTP 403", "response_time": 0.7889165878295898, "has_product_data": false}, {"name": "仅会话Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7687, "error": "HTTP 403", "response_time": 0.8442394733428955, "has_product_data": false}, {"name": "仅会话Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 7487, "error": "HTTP 403", "response_time": 0.7926840782165527, "has_product_data": false}, {"name": "仅会话<PERSON>ie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 7543, "error": "HTTP 403", "response_time": 0.9449925422668457, "has_product_data": false}, {"name": "仅安全Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7138, "error": "HTTP 403", "response_time": 0.8079125881195068, "has_product_data": false}, {"name": "仅安全Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7063, "error": "HTTP 403", "response_time": 1.703235387802124, "has_product_data": false}, {"name": "仅安全Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7138, "error": "HTTP 403", "response_time": 1.0400774478912354, "has_product_data": false}, {"name": "仅安全Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7091, "error": "HTTP 403", "response_time": 0.6805887222290039, "has_product_data": false}, {"name": "仅安全Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 6863, "error": "HTTP 403", "response_time": 0.796452522277832, "has_product_data": false}, {"name": "仅安全Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 6919, "error": "HTTP 403", "response_time": 0.890491247177124, "has_product_data": false}, {"name": "最小化Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7762, "error": "HTTP 403", "response_time": 0.6532139778137207, "has_product_data": false}, {"name": "最小化Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7715, "error": "HTTP 403", "response_time": 0.7777974605560303, "has_product_data": false}, {"name": "最小化Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7762, "error": "HTTP 403", "response_time": 0.947319746017456, "has_product_data": false}, {"name": "最小化Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7659, "error": "HTTP 403", "response_time": 0.8401002883911133, "has_product_data": false}, {"name": "最小化Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 7515, "error": "HTTP 403", "response_time": 0.9443910121917725, "has_product_data": false}, {"name": "最小化Cookie + 仅Ozon请求头", "success": false, "status_code": 403, "response_size": 7543, "error": "HTTP 403", "response_time": 0.8375999927520752, "has_product_data": false}, {"name": "无Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7138, "error": "HTTP 403", "response_time": 1.8682920932769775, "has_product_data": false}, {"name": "无Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7035, "error": "HTTP 403", "response_time": 1.0463032722473145, "has_product_data": false}, {"name": "无Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7138, "error": "HTTP 403", "response_time": 0.7397239208221436, "has_product_data": false}, {"name": "无Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7035, "error": "HTTP 403", "response_time": 1.1494455337524414, "has_product_data": false}, {"name": "无Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 6831, "error": "HTTP 403", "response_time": 0.7456257343292236, "has_product_data": false}, {"name": "无Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 6919, "error": "HTTP 403", "response_time": 0.8447809219360352, "has_product_data": false}, {"name": "核心Cookie + 完整请求头", "success": false, "status_code": 403, "response_size": 7790, "error": "HTTP 403", "response_time": 1.7287876605987549, "has_product_data": false}, {"name": "核心Cookie + 基础请求头", "success": false, "status_code": 403, "response_size": 7687, "error": "HTTP 403", "response_time": 0.8307747840881348, "has_product_data": false}, {"name": "核心Cookie + 基础+<PERSON><PERSON>请求头", "success": false, "status_code": 403, "response_size": 7762, "error": "HTTP 403", "response_time": 0.780750036239624, "has_product_data": false}, {"name": "核心Cookie + 基础+Chrome请求头", "success": false, "status_code": 403, "response_size": 7687, "error": "HTTP 403", "response_time": 0.7924621105194092, "has_product_data": false}, {"name": "核心Cookie + 最小化请求头", "success": false, "status_code": 403, "response_size": 7515, "error": "HTTP 403", "response_time": 0.7874932289123535, "has_product_data": false}, {"name": "核心Cookie + 仅<PERSON>on请求头", "success": false, "status_code": 403, "response_size": 7543, "error": "HTTP 403", "response_time": 0.8054971694946289, "has_product_data": false}]