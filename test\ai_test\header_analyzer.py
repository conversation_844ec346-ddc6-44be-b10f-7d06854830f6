#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
请求头分析工具 - 专门分析HTTP请求头的作用和重要性
"""

import sys
import os
import json
import re
from datetime import datetime
from typing import Dict, List, Optional, Any

class HeaderAnalyzer:
    """请求头分析器"""
    
    def __init__(self):
        # 从成功请求中提取的请求头
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'referer': 'https://www.ozon.ru/product/nabor-stolovoy-posudy-shiping-iz-10-predm-kolichestvo-person-10-1727409461/?_bctx=CAYQmO81&at=BrtzxwvK9H0k8rG6TLq7j9qh0OXzGGiD9JpOgIvloq8j',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.3351.109',
            'sec-ch-ua-full-version-list': '"Not)A;Brand";v="*******", "Chromium";v="138.0.7204.169", "Microsoft Edge";v="138.0.3351.109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'x-o3-app-name': 'dweb_client',
            'x-o3-app-version': 'release_30-6-2025_551b2394',
            'x-o3-manifest-version': 'frontend-ozon-ru:551b23941a9d92fb35bd2c7850fae943cee48228,sf-render-api:100a986a9257dfdf22e7c029f43488da3e00e497',
            'x-o3-parent-requestid': '53f00ae1cd7aebb49bff904ecfb0b03d',
            'x-page-view-id': '56537c5d-1942-47eb-7dfc-a582686a7bb1'
        }

    def analyze_header_structure(self, header_name: str, header_value: str) -> Dict[str, Any]:
        """分析单个请求头的结构"""
        analysis = {
            "name": header_name,
            "value": header_value,
            "length": len(header_value),
            "category": "unknown",
            "purpose": "unknown",
            "importance": "medium",
            "components": {},
            "security_related": False,
            "browser_specific": False,
            "ozon_specific": False
        }
        
        # 分析不同类型的请求头
        if header_name == "accept":
            analysis.update({
                "category": "content_negotiation",
                "purpose": "指定客户端能够接收的内容类型",
                "importance": "high",
                "components": {"content_types": header_value.split(",")}
            })
            
        elif header_name == "accept-language":
            analysis.update({
                "category": "content_negotiation", 
                "purpose": "指定客户端偏好的语言",
                "importance": "medium",
                "components": {"languages": self._parse_accept_language(header_value)}
            })
            
        elif header_name == "content-type":
            analysis.update({
                "category": "content_negotiation",
                "purpose": "指定请求体的内容类型",
                "importance": "high"
            })
            
        elif header_name == "user-agent":
            analysis.update({
                "category": "client_identification",
                "purpose": "标识客户端浏览器和操作系统",
                "importance": "critical",
                "browser_specific": True,
                "components": self._parse_user_agent(header_value)
            })
            
        elif header_name == "referer":
            analysis.update({
                "category": "navigation",
                "purpose": "指示请求来源页面",
                "importance": "high",
                "components": {"source_url": header_value}
            })
            
        elif header_name.startswith("sec-ch-ua"):
            analysis.update({
                "category": "client_hints",
                "purpose": "提供客户端浏览器信息（Client Hints）",
                "importance": "medium",
                "security_related": True,
                "browser_specific": True
            })
            
        elif header_name.startswith("sec-fetch-"):
            analysis.update({
                "category": "fetch_metadata",
                "purpose": "提供请求的安全上下文信息",
                "importance": "high",
                "security_related": True
            })
            
        elif header_name.startswith("x-o3-"):
            analysis.update({
                "category": "ozon_specific",
                "purpose": "Ozon平台特定的请求头",
                "importance": "critical",
                "ozon_specific": True
            })
            
        elif header_name == "priority":
            analysis.update({
                "category": "performance",
                "purpose": "指定请求优先级",
                "importance": "low"
            })
            
        # 特殊分析
        if header_name == "x-o3-app-name":
            analysis["components"]["app_name"] = header_value
        elif header_name == "x-o3-app-version":
            analysis["components"]["app_version"] = header_value
        elif header_name == "x-o3-manifest-version":
            analysis["components"]["manifest_info"] = self._parse_manifest_version(header_value)
        elif header_name in ["x-o3-parent-requestid", "x-page-view-id"]:
            analysis["components"]["tracking_id"] = header_value
            
        return analysis

    def _parse_accept_language(self, value: str) -> List[Dict[str, str]]:
        """解析Accept-Language头"""
        languages = []
        for lang_part in value.split(','):
            lang_part = lang_part.strip()
            if ';q=' in lang_part:
                lang, quality = lang_part.split(';q=')
                languages.append({"language": lang.strip(), "quality": quality.strip()})
            else:
                languages.append({"language": lang_part, "quality": "1.0"})
        return languages

    def _parse_user_agent(self, value: str) -> Dict[str, str]:
        """解析User-Agent头"""
        components = {}
        
        # 提取操作系统
        if "Windows NT" in value:
            os_match = re.search(r'Windows NT ([\d.]+)', value)
            components["os"] = f"Windows NT {os_match.group(1)}" if os_match else "Windows"
        elif "Mac OS X" in value:
            components["os"] = "macOS"
        elif "Linux" in value:
            components["os"] = "Linux"
        
        # 提取浏览器
        if "Edg/" in value:
            edge_match = re.search(r'Edg/([\d.]+)', value)
            components["browser"] = f"Microsoft Edge {edge_match.group(1)}" if edge_match else "Microsoft Edge"
        elif "Chrome/" in value:
            chrome_match = re.search(r'Chrome/([\d.]+)', value)
            components["browser"] = f"Chrome {chrome_match.group(1)}" if chrome_match else "Chrome"
        elif "Firefox/" in value:
            firefox_match = re.search(r'Firefox/([\d.]+)', value)
            components["browser"] = f"Firefox {firefox_match.group(1)}" if firefox_match else "Firefox"
        
        # 提取架构
        if "x64" in value or "Win64" in value:
            components["architecture"] = "x64"
        elif "x86" in value:
            components["architecture"] = "x86"
        
        return components

    def _parse_manifest_version(self, value: str) -> Dict[str, str]:
        """解析manifest版本信息"""
        components = {}
        parts = value.split(',')
        for part in parts:
            if ':' in part:
                key, version = part.split(':', 1)
                components[key.strip()] = version.strip()
        return components

    def analyze_all_headers(self):
        """分析所有请求头"""
        print("=" * 80)
        print("请求头详细分析报告")
        print("=" * 80)
        
        analyses = []
        for header_name, header_value in self.headers.items():
            analysis = self.analyze_header_structure(header_name, header_value)
            analyses.append(analysis)
            
            print(f"\n📋 请求头: {header_name}")
            print(f"   分类: {analysis['category']}")
            print(f"   用途: {analysis['purpose']}")
            print(f"   重要性: {analysis['importance']}")
            print(f"   长度: {analysis['length']} 字符")
            
            flags = []
            if analysis['security_related']:
                flags.append("安全相关")
            if analysis['browser_specific']:
                flags.append("浏览器特定")
            if analysis['ozon_specific']:
                flags.append("Ozon特定")
            
            if flags:
                print(f"   标志: {', '.join(flags)}")
            
            if analysis['components']:
                print("   组件:")
                for key, value in analysis['components'].items():
                    if isinstance(value, list):
                        print(f"     - {key}: {len(value)} 项")
                    elif isinstance(value, dict):
                        print(f"     - {key}: {len(value)} 个键值对")
                    else:
                        display_value = str(value)[:50] + "..." if len(str(value)) > 50 else str(value)
                        print(f"     - {key}: {display_value}")
        
        return analyses

    def generate_header_importance_ranking(self):
        """生成请求头重要性排名"""
        print("\n" + "=" * 80)
        print("请求头重要性排名")
        print("=" * 80)
        
        # 重要性评分规则
        importance_scores = {
            "critical": 10,
            "high": 7,
            "medium": 4,
            "low": 1
        }
        
        category_bonus = {
            "ozon_specific": 5,
            "client_identification": 3,
            "fetch_metadata": 2,
            "content_negotiation": 1,
            "client_hints": 1,
            "navigation": 1,
            "performance": 0
        }
        
        header_rankings = []
        for header_name, header_value in self.headers.items():
            analysis = self.analyze_header_structure(header_name, header_value)
            
            base_score = importance_scores.get(analysis['importance'], 1)
            category_score = category_bonus.get(analysis['category'], 0)
            
            # 特殊加分
            bonus = 0
            if analysis['ozon_specific']:
                bonus += 3
            if analysis['security_related']:
                bonus += 2
            
            total_score = base_score + category_score + bonus
            
            header_rankings.append({
                "name": header_name,
                "category": analysis['category'],
                "importance": analysis['importance'],
                "score": total_score,
                "length": analysis['length'],
                "ozon_specific": analysis['ozon_specific'],
                "security_related": analysis['security_related']
            })
        
        # 按分数排序
        header_rankings.sort(key=lambda x: x['score'], reverse=True)
        
        print("排名 | 请求头名称 | 分类 | 重要性 | 分数 | 特殊标志")
        print("-" * 80)
        
        for i, header in enumerate(header_rankings, 1):
            flags = []
            if header['ozon_specific']:
                flags.append("Ozon")
            if header['security_related']:
                flags.append("安全")
            
            flag_str = ",".join(flags) if flags else "-"
            
            print(f"{i:2d}   | {header['name']:<25} | {header['category']:<15} | {header['importance']:<8} | {header['score']:2d}   | {flag_str}")
        
        return header_rankings

    def suggest_minimal_header_set(self):
        """建议最小请求头集合"""
        print("\n" + "=" * 80)
        print("最小请求头集合建议")
        print("=" * 80)
        
        rankings = self.generate_header_importance_ranking()
        
        # 必需请求头（分数>=10）
        essential_headers = [h for h in rankings if h['score'] >= 10]
        # 重要请求头（分数7-9）
        important_headers = [h for h in rankings if 7 <= h['score'] < 10]
        # 可选请求头（分数<7）
        optional_headers = [h for h in rankings if h['score'] < 7]
        
        print("🔴 必需请求头（推荐保留）:")
        for header in essential_headers:
            print(f"  - {header['name']} ({header['category']})")
        
        print(f"\n🟡 重要请求头（建议保留）:")
        for header in important_headers:
            print(f"  - {header['name']} ({header['category']})")
        
        print(f"\n🟢 可选请求头（可以移除）:")
        for header in optional_headers:
            print(f"  - {header['name']} ({header['category']})")
        
        # 生成最小集合
        minimal_set = [h['name'] for h in essential_headers + important_headers]
        
        print(f"\n推荐的最小请求头集合 ({len(minimal_set)} 个):")
        for header_name in minimal_set:
            print(f"  - {header_name}")
        
        return minimal_set

    def analyze_ozon_specific_headers(self):
        """专门分析Ozon特定的请求头"""
        print("\n" + "=" * 80)
        print("Ozon特定请求头分析")
        print("=" * 80)
        
        ozon_headers = {k: v for k, v in self.headers.items() if k.startswith('x-o3-') or k.startswith('x-page-')}
        
        for header_name, header_value in ozon_headers.items():
            analysis = self.analyze_header_structure(header_name, header_value)
            
            print(f"\n🔧 {header_name}:")
            print(f"   值: {header_value}")
            print(f"   用途: {analysis['purpose']}")
            
            if analysis['components']:
                print("   解析结果:")
                for key, value in analysis['components'].items():
                    print(f"     - {key}: {value}")
        
        print(f"\nOzon特定请求头总数: {len(ozon_headers)}")
        print("这些请求头对于Ozon API调用可能是必需的。")

    def export_analysis_report(self):
        """导出分析报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"header_analysis_report_{timestamp}.json"
        
        analyses = []
        for header_name, header_value in self.headers.items():
            analysis = self.analyze_header_structure(header_name, header_value)
            analyses.append(analysis)
        
        rankings = self.generate_header_importance_ranking()
        minimal_set = self.suggest_minimal_header_set()
        
        report = {
            "timestamp": timestamp,
            "total_headers": len(self.headers),
            "header_analyses": analyses,
            "importance_rankings": rankings,
            "minimal_header_set": minimal_set,
            "summary": {
                "essential_headers": len([h for h in rankings if h['score'] >= 10]),
                "important_headers": len([h for h in rankings if 7 <= h['score'] < 10]),
                "optional_headers": len([h for h in rankings if h['score'] < 7]),
                "ozon_specific_headers": len([h for h in rankings if h['ozon_specific']]),
                "security_related_headers": len([h for h in rankings if h['security_related']])
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已导出到: {filename}")
        return filename

def main():
    """主程序"""
    analyzer = HeaderAnalyzer()
    
    print("请求头分析工具")
    print("1. 分析所有请求头")
    print("2. 生成重要性排名")
    print("3. 建议最小请求头集合")
    print("4. 分析Ozon特定请求头")
    print("5. 导出完整分析报告")
    print("6. 执行全部分析")
    
    choice = input("请选择操作 (1-6): ").strip()
    
    if choice == "1":
        analyzer.analyze_all_headers()
    elif choice == "2":
        analyzer.generate_header_importance_ranking()
    elif choice == "3":
        analyzer.suggest_minimal_header_set()
    elif choice == "4":
        analyzer.analyze_ozon_specific_headers()
    elif choice == "5":
        analyzer.export_analysis_report()
    elif choice == "6":
        analyzer.analyze_all_headers()
        analyzer.generate_header_importance_ranking()
        analyzer.suggest_minimal_header_set()
        analyzer.analyze_ozon_specific_headers()
        analyzer.export_analysis_report()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
