#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie 管理模块
专门处理 Ozon 反爬所需的最小化 Cookie 配置
"""

import re
from typing import Optional
from .logger import scraping_logger

class CookieManager:
    """Cookie 管理器"""
    
    def __init__(self):
        """初始化 Cookie 管理器"""
        self.abt_data_pattern = re.compile(r'abt_data=([^;]+)')
    
    def extract_abt_data(self, full_cookie: str) -> Optional[str]:
        """
        从完整的 Cookie 字符串中提取 abt_data 值
        
        Args:
            full_cookie (str): 完整的 Cookie 字符串
            
        Returns:
            Optional[str]: 提取的 abt_data 值，如果未找到则返回 None
        """
        if not full_cookie:
            scraping_logger.logger.warning("Cookie 字符串为空")
            return None
        
        match = self.abt_data_pattern.search(full_cookie)
        if match:
            abt_data_value = match.group(1)
            scraping_logger.logger.info(f"成功提取 abt_data，长度: {len(abt_data_value)}")
            return abt_data_value
        else:
            scraping_logger.logger.error("未找到 abt_data 值")
            return None
    
    def create_minimal_cookie(self, full_cookie: str) -> Optional[str]:
        """
        创建只包含 abt_data 的最小化 Cookie
        
        Args:
            full_cookie (str): 完整的 Cookie 字符串
            
        Returns:
            Optional[str]: 最小化的 Cookie 字符串
        """
        abt_data_value = self.extract_abt_data(full_cookie)
        if abt_data_value:
            minimal_cookie = f"abt_data={abt_data_value}"
            scraping_logger.logger.info("创建最小化 Cookie 成功")
            return minimal_cookie
        return None
    
    def validate_abt_data(self, abt_data_value: str) -> bool:
        """
        验证 abt_data 值的有效性
        
        Args:
            abt_data_value (str): abt_data 值
            
        Returns:
            bool: 是否有效
        """
        if not abt_data_value:
            return False
        
        # abt_data 通常以数字开头，包含特定字符
        if not abt_data_value.startswith('7.'):
            scraping_logger.logger.warning("abt_data 格式可能不正确，应以 '7.' 开头")
            return False
        
        # 检查长度（通常很长）
        if len(abt_data_value) < 100:
            scraping_logger.logger.warning("abt_data 长度可能不足")
            return False
        
        scraping_logger.logger.info("abt_data 验证通过")
        return True
    
    def get_api_cookie(self, full_cookie: str) -> str:
        """
        获取用于 API 请求的 Cookie
        
        Args:
            full_cookie (str): 完整的 Cookie 字符串
            
        Returns:
            str: 用于 API 请求的 Cookie 字符串
        """
        minimal_cookie = self.create_minimal_cookie(full_cookie)
        if minimal_cookie:
            return minimal_cookie
        
        # 如果提取失败，记录警告并返回原始 Cookie
        scraping_logger.logger.warning("无法创建最小化 Cookie，使用原始 Cookie")
        return full_cookie
    
    def get_html_cookie(self, full_cookie: str) -> str:
        """
        获取用于 HTML 请求的 Cookie（可能需要更多信息）
        
        Args:
            full_cookie (str): 完整的 Cookie 字符串
            
        Returns:
            str: 用于 HTML 请求的 Cookie 字符串
        """
        # HTML 请求可能需要更多 Cookie 信息，暂时返回完整 Cookie
        return full_cookie
    
    def log_cookie_info(self, full_cookie: str):
        """
        记录 Cookie 信息用于调试
        
        Args:
            full_cookie (str): 完整的 Cookie 字符串
        """
        if not full_cookie:
            scraping_logger.logger.error("Cookie 为空")
            return
        
        # 统计 Cookie 中的键值对数量
        cookie_pairs = [pair.strip() for pair in full_cookie.split(';') if pair.strip()]
        scraping_logger.logger.info(f"Cookie 包含 {len(cookie_pairs)} 个键值对")
        
        # 检查关键字段
        key_fields = ['abt_data', '__Secure-access-token', '__Secure-user-id', 'sc_company_id']
        found_fields = []
        
        for field in key_fields:
            if field in full_cookie:
                found_fields.append(field)
        
        scraping_logger.logger.info(f"找到关键字段: {', '.join(found_fields)}")
        
        # 特别检查 abt_data
        abt_data_value = self.extract_abt_data(full_cookie)
        if abt_data_value:
            scraping_logger.logger.info(f"abt_data 长度: {len(abt_data_value)}")
            scraping_logger.logger.info(f"abt_data 前缀: {abt_data_value[:20]}...")
        else:
            scraping_logger.logger.error("未找到 abt_data")

# 全局 Cookie 管理器实例
cookie_manager = CookieManager()

def get_api_cookie(full_cookie: str) -> str:
    """
    便捷函数：获取 API 请求用的 Cookie
    
    Args:
        full_cookie (str): 完整的 Cookie 字符串
        
    Returns:
        str: 最小化的 Cookie 字符串
    """
    return cookie_manager.get_api_cookie(full_cookie)

def get_html_cookie(full_cookie: str) -> str:
    """
    便捷函数：获取 HTML 请求用的 Cookie
    
    Args:
        full_cookie (str): 完整的 Cookie 字符串
        
    Returns:
        str: HTML 请求用的 Cookie 字符串
    """
    return cookie_manager.get_html_cookie(full_cookie)

def validate_cookie(full_cookie: str) -> bool:
    """
    便捷函数：验证 Cookie 有效性
    
    Args:
        full_cookie (str): 完整的 Cookie 字符串
        
    Returns:
        bool: Cookie 是否有效
    """
    abt_data_value = cookie_manager.extract_abt_data(full_cookie)
    if abt_data_value:
        return cookie_manager.validate_abt_data(abt_data_value)
    return False

# 使用示例
if __name__ == "__main__":
    # 测试用例
    test_cookie = "abt_data=7.test_value_here; other_cookie=value"
    
    print("=== Cookie 管理器测试 ===")
    print(f"原始 Cookie: {test_cookie}")
    print(f"API Cookie: {get_api_cookie(test_cookie)}")
    print(f"HTML Cookie: {get_html_cookie(test_cookie)}")
    print(f"Cookie 有效性: {validate_cookie(test_cookie)}")
