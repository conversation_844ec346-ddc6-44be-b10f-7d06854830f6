# modules/api_client.py
from curl_cffi import requests  # 使用 curl_cffi 替代标准 requests 以绕过反爬
import json
import time
import random
from typing import Dict, List, Optional, Tuple
from config import REQUEST_TIMEOUT, MAX_RETRIES, RETRY_DELAY
from .logger import api_logger, log_exception, log_performance



class OzonApiClient:
    def __init__(self, client_id: str, api_key: str, base_url: str):
        self.client_id = client_id
        self.api_key = api_key
        self.base_url = base_url

        # 基于测试结果的最佳浏览器模拟选项
        self.best_impersonations = ["chrome104", "chrome110", "chrome99", "edge99"]

        # 基础请求头
        self.base_headers = {
            "Client-Id": self.client_id,
            "Api-Key": self.api_key,
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"
        }

    @log_performance
    def _post(self, endpoint: str, payload: dict) -> dict:
        """通用的POST请求方法，包含错误处理和重试机制。"""
        url = self.base_url + endpoint

        for attempt in range(MAX_RETRIES):
            start_time = time.time()

            # 随机选择最佳的浏览器模拟（基于测试结果）
            impersonation = random.choice(self.best_impersonations)

            try:
                # 记录请求开始
                api_logger.log_request(endpoint, payload, attempt + 1)

                # 使用curl_cffi发送请求，不使用session以避免冲突
                response = requests.post(
                    url,
                    data=json.dumps(payload),
                    headers=self.base_headers,
                    timeout=REQUEST_TIMEOUT,
                    impersonate=impersonation,  # 使用测试验证的最佳浏览器模拟
                    verify=False,  # 基于测试结果，跳过SSL验证
                    allow_redirects=True
                )
                response.raise_for_status()

                result = response.json()
                duration = time.time() - start_time

                # 记录成功响应
                api_logger.log_response(endpoint, response.status_code, result, duration)
                return result

            except Exception as e:
                # curl_cffi 使用不同的异常处理方式
                if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
                    duration = time.time() - start_time

                    # 获取详细错误信息
                    error_detail = None
                    try:
                        error_detail = e.response.json()
                    except:
                        error_detail = e.response.text

                    # 记录HTTP错误
                    api_logger.log_error(endpoint, e, attempt + 1)

                    # 如果是429错误（请求过于频繁），等待更长时间
                    if e.response.status_code == 429:
                        wait_time = RETRY_DELAY * (2 ** attempt)
                        api_logger.log_retry(endpoint, attempt + 1, MAX_RETRIES, wait_time)
                        time.sleep(wait_time)
                        continue

                    # 对于其他HTTP错误，如果是最后一次尝试，抛出异常
                    if attempt == MAX_RETRIES - 1:
                        raise
                else:
                    # 其他类型的异常（网络错误等）
                    api_logger.log_error(endpoint, e, attempt + 1)
                    if attempt == MAX_RETRIES - 1:
                        raise

            # 等待后重试
            if attempt < MAX_RETRIES - 1:
                wait_time = RETRY_DELAY * (attempt + 1)
                api_logger.log_retry(endpoint, attempt + 1, MAX_RETRIES, wait_time)
                time.sleep(wait_time)

        error_msg = f"API请求失败，已重试 {MAX_RETRIES} 次"
        api_logger.logger.error(error_msg)
        raise Exception(error_msg)

    def get_category_tree(self, language: str = "DEFAULT") -> List[dict]:
        """获取完整的Ozon商品类目树。"""
        payload = {"language": language}
        response = self._post("/v1/description-category/tree", payload)
        return response.get("result", [])

    def get_category_attributes(self, description_category_id: int, type_id: int, language: str = "DEFAULT") -> List[dict]:
        """根据类目ID和类型ID，获取该类目下的所有属性定义。

        Args:
            description_category_id: 类目ID，应使用 Level 3 类目ID (如 53968796)
            type_id: 类型ID (如 971080300)
            language: 语言设置，默认为 "DEFAULT"

        Returns:
            属性列表
        """
        payload = {
            "description_category_id": description_category_id,  # 使用正确的参数名
            "type_id": type_id,
            "language": language
        }
        response = self._post("/v1/description-category/attribute", payload)
        return response.get("result", [])

    def get_attribute_values(self, description_category_id: int, attribute_id: int, type_id: int,
                           last_value_id: int = 0, limit: int = 2000,
                           language: str = "DEFAULT") -> List[dict]:
        """获取特定属性的字典值列表。

        Args:
            description_category_id: 类目ID，使用 description_category_id
            attribute_id: 属性ID
            type_id: 商品类型ID
            last_value_id: 启动响应的指南ID，默认为0
            limit: 响应中值的数量，最多2000，默认2000
            language: 回复语言，默认"DEFAULT"

        Returns:
            属性值列表
        """
        payload = {
            "description_category_id": description_category_id,  # 使用正确的参数名
            "attribute_id": attribute_id,
            "type_id": type_id,  # 必需的参数
            "language": language,
            "last_value_id": last_value_id,
            "limit": limit
        }
        response = self._post("/v1/description-category/attribute/values", payload)
        return response.get("result", [])

    def create_product(self, items: List[dict]) -> dict:
        """
        创建商品。items是经过完整映射和转换后的商品数据列表。
        使用v3版本的API（官方推荐）
        """
        payload = {"items": items}
        return self._post("/v3/product/import", payload)

    def check_import_status(self, task_id: int) -> dict:
        """根据任务ID检查商品创建状态。"""
        payload = {"task_id": task_id}
        return self._post("/v1/product/import/info", payload)

    def get_product_list(self, filter_params: dict = None, limit: int = 100) -> dict:
        """获取商品列表"""
        payload = {
            "filter": filter_params or {},
            "limit": limit,
            "last_id": "",
            "sort_dir": "ASC"
        }
        return self._post("/v3/product/list", payload)

    def get_product_info_list(self, offer_ids: List[str] = None, product_ids: List[int] = None) -> dict:
        """获取商品详细信息"""
        payload = {}
        if offer_ids:
            payload["offer_id"] = offer_ids
        if product_ids:
            payload["product_id"] = product_ids
        return self._post("/v3/product/info/list", payload)

    def get_product_attributes(self, filter_params: dict, limit: int = 100) -> dict:
        """获取商品属性信息"""
        payload = {
            "filter": filter_params,
            "limit": limit,
            "last_id": "",
            "sort_dir": "ASC"
        }
        return self._post("/v3/products/info/attributes", payload)

    def update_product_attributes(self, items: List[dict]) -> dict:
        """更新商品属性"""
        payload = {"items": items}
        return self._post("/v1/product/attributes/update", payload)

    def update_product_prices(self, prices: List[dict]) -> dict:
        """更新商品价格"""
        payload = {"prices": prices}
        return self._post("/v1/product/import/prices", payload)

    def get_product_prices(self, filter_params: dict, limit: int = 100) -> dict:
        """获取商品价格信息"""
        payload = {
            "filter": filter_params,
            "limit": limit,
            "last_id": "",
            "sort_dir": "ASC"
        }
        return self._post("/v5/product/info/prices", payload)

    def update_product_stocks(self, stocks: List[dict]) -> dict:
        """更新商品库存"""
        payload = {"stocks": stocks}
        return self._post("/v2/products/stocks", payload)

    def get_product_stocks(self, filter_params: dict, limit: int = 100) -> dict:
        """获取商品库存信息"""
        payload = {
            "filter": filter_params,
            "limit": limit,
            "last_id": "",
            "sort_dir": "ASC"
        }
        return self._post("/v4/product/info/stocks", payload)

    def get_warehouse_stocks(self, filter_params: dict, limit: int = 100) -> dict:
        """获取仓库库存信息"""
        payload = {
            "filter": filter_params,
            "limit": limit,
            "last_id": "",
            "sort_dir": "ASC"
        }
        return self._post("/v1/product/info/stocks-by-warehouse/fbs", payload)

    def archive_product(self, product_ids: List[int]) -> dict:
        """将商品存档"""
        payload = {"product_id": product_ids}
        return self._post("/v1/product/archive", payload)

    def unarchive_product(self, product_ids: List[int]) -> dict:
        """从存档中恢复商品"""
        payload = {"product_id": product_ids}
        return self._post("/v1/product/unarchive", payload)

    def delete_product(self, products: List[dict]) -> dict:
        """删除商品"""
        payload = {"products": products}
        return self._post("/v2/products/delete", payload)

    def get_warehouse_list(self) -> dict:
        """获取仓库列表"""
        return self._post("/v1/warehouse/list", {})

    def get_delivery_methods(self) -> dict:
        """获取配送方式列表"""
        return self._post("/v1/delivery-method/list", {})

    def upload_product_pictures(self, product_id: int, images: List[str]) -> dict:
        """上传商品图片"""
        payload = {
            "product_id": product_id,
            "images": [{"file_name": img, "default": i == 0} for i, img in enumerate(images)]
        }
        return self._post("/v1/product/pictures/import", payload)

    def get_picture_upload_status(self, task_ids: List[int]) -> dict:
        """检查图片上传状态"""
        payload = {"task_id": task_ids}
        return self._post("/v2/product/pictures/info", payload)

    def create_report(self, report_type: str, filter_params: dict = None) -> dict:
        """创建报告"""
        payload = {
            "filter": filter_params or {},
            "language": "DEFAULT"
        }

        report_endpoints = {
            "products": "/v1/report/products/create",
            "warehouse_stock": "/v1/report/warehouse/stock",
            "returns": "/v2/report/returns/create",
            "postings": "/v1/report/postings/create",
            "discounted": "/v1/report/discounted/create"
        }

        endpoint = report_endpoints.get(report_type, "/v1/report/products/create")
        return self._post(endpoint, payload)

    def get_report_info(self, code: str) -> dict:
        """获取报告信息"""
        payload = {"code": code}
        return self._post("/v1/report/info", payload)

    def get_report_list(self, page: int = 1, page_size: int = 100) -> dict:
        """获取报告列表"""
        payload = {
            "page": page,
            "page_size": page_size
        }
        return self._post("/v1/report/list", payload)

    def find_category_by_path(self, category_tree: List[dict], category_path: List[str]) -> Optional[Tuple[int, int]]:
        """
        在类目树中根据类目路径查找category_id和type_id

        Args:
            category_tree: 类目树数据
            category_path: 类目路径，如 ["电子产品", "智能手机"]

        Returns:
            Tuple[category_id, type_id] 或 None
        """
        def search_in_tree(nodes: List[dict], path: List[str], depth: int = 0) -> Optional[Tuple[int, int]]:
            if depth >= len(path):
                return None

            target_name = path[depth].lower()
            print(f"  搜索第{depth+1}级类目: '{target_name}'")

            # 首先尝试精确匹配
            for node in nodes:
                node_name = (node.get("category_name") or node.get("type_name") or node.get("title", "")).lower()
                if target_name == node_name:
                    print(f"    精确匹配: '{node_name}' (深度: {depth})")

                    if depth == len(path) - 1:
                        category_id = node.get("description_category_id") or node.get("category_id")
                        type_id = node.get("type_id")
                        if category_id and type_id:
                            print(f"    最终匹配: category_id={category_id}, type_id={type_id}")
                            return (category_id, type_id)

                    children = node.get("children", [])
                    if children:
                        result = search_in_tree(children, path, depth + 1)
                        if result:
                            return result

            # 如果精确匹配失败，尝试跳过当前级别，直接搜索下一级
            if depth < len(path) - 1:
                print(f"    尝试跳过第{depth+1}级，直接搜索下一级")
                for node in nodes:
                    children = node.get("children", [])
                    if children:
                        result = search_in_tree(children, path, depth + 1)
                        if result:
                            return result

            # 最后尝试模糊匹配
            for node in nodes:
                node_name = (node.get("category_name") or node.get("type_name") or node.get("title", "")).lower()
                if target_name in node_name or node_name in target_name:
                    print(f"    模糊匹配: '{node_name}' (深度: {depth})")

                    if depth == len(path) - 1:
                        category_id = node.get("description_category_id") or node.get("category_id")
                        type_id = node.get("type_id")
                        if category_id and type_id:
                            print(f"    最终匹配: category_id={category_id}, type_id={type_id}")
                            return (category_id, type_id)

                    children = node.get("children", [])
                    if children:
                        result = search_in_tree(children, path, depth + 1)
                        if result:
                            return result

            print(f"  第{depth+1}级类目 '{target_name}' 未找到匹配")
            return None

        print(f"开始搜索类目路径: {' > '.join(category_path)}")

        # 特殊处理：如果是餐具类商品，直接查找相关类目
        if len(category_path) >= 3 and "посуда" in category_path[-2].lower():
            print("检测到餐具类商品，使用特殊匹配逻辑")
            for cat in category_tree:
                if cat.get('category_name', '').lower() == 'дом и сад':
                    for child in cat.get('children', []):
                        child_name = child.get('category_name', child.get('type_name', ''))
                        if 'столовая посуда' in child_name.lower():
                            print(f"找到餐具类目: {child_name}")
                            # 查找具体的餐具类型，优先匹配餐具套装
                            best_match = None
                            for grandchild in child.get('children', []):
                                gc_name = grandchild.get('type_name', '')
                                category_id = child.get('description_category_id')
                                type_id = grandchild.get('type_id')

                                if category_id and type_id:
                                    # 优先匹配餐具套装
                                    if 'набор' in gc_name.lower() and 'посуд' in gc_name.lower():
                                        print(f"找到最佳匹配的餐具套装类目: {gc_name}")
                                        print(f"category_id={category_id}, type_id={type_id}")
                                        return (category_id, type_id)
                                    # 其次匹配服务套装
                                    elif 'сервиз' in gc_name.lower() and not best_match:
                                        best_match = (category_id, type_id, gc_name)

                            # 如果没有找到餐具套装，使用服务套装
                            if best_match:
                                category_id, type_id, gc_name = best_match
                                print(f"找到备选匹配的餐具类目: {gc_name}")
                                print(f"category_id={category_id}, type_id={type_id}")
                                return (category_id, type_id)
                    break

        return search_in_tree(category_tree, category_path)

    def find_category_by_name(self, category_tree: List[dict], category_name: str) -> List[Tuple[int, int, str]]:
        """
        根据类目名称查找所有匹配的类目

        Args:
            category_tree: 类目树数据
            category_name: 类目名称

        Returns:
            List[Tuple[category_id, type_id, full_path]] 匹配的类目列表
        """
        results = []

        def search_recursive(nodes: List[dict], path: str = ""):
            for node in nodes:
                node_name = node.get("category_name") or node.get("title", "")
                current_path = f"{path} > {node_name}" if path else node_name

                # 检查是否匹配
                if category_name.lower() in node_name.lower():
                    category_id = node.get("description_category_id") or node.get("category_id")
                    type_id = node.get("type_id")
                    if category_id and type_id:
                        results.append((category_id, type_id, current_path))

                # 递归搜索子类目
                children = node.get("children", [])
                if children:
                    search_recursive(children, current_path)

        search_recursive(category_tree)
        return results

    def match_attribute_value(self, attribute_values: List[dict], target_value: str) -> Optional[int]:
        """
        在属性值列表中匹配目标值，返回对应的dictionary_value_id
        
        Args:
            attribute_values: 属性值列表
            target_value: 目标值
            
        Returns:
            dictionary_value_id 或 None
        """
        target_lower = target_value.lower().strip()
        
        # 精确匹配
        for value_item in attribute_values:
            value_text = value_item.get("value", "").lower().strip()
            if value_text == target_lower:
                return value_item.get("id")
        
        # 模糊匹配
        for value_item in attribute_values:
            value_text = value_item.get("value", "").lower().strip()
            if target_lower in value_text or value_text in target_lower:
                return value_item.get("id")
        
        return None

    def validate_product_data(self, product_data: dict, required_attributes: List[dict]) -> Tuple[bool, List[str]]:
        """
        验证商品数据是否满足必填属性要求
        
        Args:
            product_data: 商品数据
            required_attributes: 必填属性列表
            
        Returns:
            Tuple[是否有效, 错误信息列表]
        """
        errors = []
        
        # 检查基本字段
        required_fields = ["name", "price", "offer_id"]
        for field in required_fields:
            if not product_data.get(field):
                errors.append(f"缺少必填字段: {field}")
        
        # 检查必填属性
        product_attributes = {attr.get("id"): attr for attr in product_data.get("attributes", [])}
        
        for attr_def in required_attributes:
            if attr_def.get("is_required") and attr_def.get("id") not in product_attributes:
                errors.append(f"缺少必填属性: {attr_def.get('name')} (ID: {attr_def.get('id')})")
        
        return len(errors) == 0, errors

    def get_product_info(self, product_id: str) -> Optional[dict]:
        """获取现有商品信息（用于调试和验证）"""
        try:
            payload = {"product_id": [product_id]}
            response = self._post("/v2/product/info", payload)
            result = response.get("result", {})
            if result and "items" in result and result["items"]:
                return result["items"][0]
        except Exception as e:
            print(f"获取商品信息失败: {e}")
        return None



    def wait_for_import_completion(self, task_id: int, max_wait_time: int = 300,
                                 check_interval: int = 10) -> Tuple[bool, dict]:
        """
        等待商品导入完成

        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            check_interval: 检查间隔（秒）

        Returns:
            Tuple[是否成功, 最终状态信息]
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                status_info = self.check_import_status(task_id)
                result = status_info.get("result", {})

                # 检查任务状态
                items = result.get("items", [])
                if items:
                    item = items[0]
                    status = item.get("status")

                    print(f"导入状态: {status}")

                    if status == "imported":
                        print("商品导入成功！")
                        return True, result
                    elif status in ["failed", "error"]:
                        print(f"商品导入失败: {item.get('errors', [])}")
                        return False, result
                    elif status in ["pending", "processing"]:
                        print(f"商品正在处理中，{check_interval}秒后再次检查...")
                        time.sleep(check_interval)
                        continue

            except Exception as e:
                print(f"检查导入状态时出错: {e}")
                time.sleep(check_interval)

        print(f"等待超时（{max_wait_time}秒），请手动检查导入状态")
        return False, {}

    def get_roles_info(self) -> Optional[dict]:
        """获取API密钥关联的角色信息（用于验证API凭证）"""
        try:
            response = self._post("/v1/roles", {})
            # /v1/roles API直接返回角色数据，不包装在result中
            return response
        except Exception as e:
            print(f"获取角色信息失败: {e}")
            return None

    def test_api_connection(self) -> bool:
        """测试API连接是否正常"""
        try:
            roles_info = self.get_roles_info()
            if roles_info is not None:
                if isinstance(roles_info, dict) and 'roles' in roles_info:
                    roles = roles_info.get('roles', [])
                    print(f"API连接正常，角色数量: {len(roles)}")
                    if roles:
                        print(f"可用角色: {', '.join([role.get('name', 'Unknown') for role in roles[:3]])}")
                    return True
                elif isinstance(roles_info, list):
                    print(f"API连接正常，获取到 {len(roles_info)} 条角色数据")
                    return True
                else:
                    print("API连接正常")
                    return True
            else:
                print("API连接失败：无法获取角色信息")
                return False
        except Exception as e:
            print(f"API连接测试失败: {e}")
            return False

    @log_performance
    def get_category_by_sku(self, sku: str) -> Optional[Dict]:
        """
        通过 SKU 获取商品类目信息
        使用 Seller Tree API 来获取准确的类目信息

        Args:
            sku (str): 商品 SKU

        Returns:
            Optional[Dict]: 类目信息字典，包含各级类目ID和类型ID
        """
        try:
            from config import OZON_SESSION_COOKIE

            # 构建 Seller Tree API 请求
            seller_tree_url = "https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku"

            # Seller Tree API 需要完整的 Cookie，不能使用最小化版本
            api_cookie = OZON_SESSION_COOKIE

            # 设置专门的请求头
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'zh-Hans',
                'content-type': 'application/json',
                'origin': 'https://seller.ozon.ru',
                'referer': 'https://seller.ozon.ru/app/products/add/general-info',
                'sec-ch-ua': '"Chromium";v="110", "Not A(Brand";v="24", "Google Chrome";v="110"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
                'x-o3-app-name': 'seller-ui',
                'x-o3-company-id': '2977542',
                'x-o3-language': 'zh-Hans',
                'x-o3-page-type': 'products-other',
                'Cookie': api_cookie
            }

            # 请求数据
            payload = {"skus": [sku]}

            api_logger.logger.info(f"通过 Seller Tree API 查询 SKU: {sku}")

            # 发送请求
            response = requests.post(
                seller_tree_url,
                headers=headers,
                json=payload,
                timeout=REQUEST_TIMEOUT,
                impersonate="chrome110",
                verify=True
            )

            if response.status_code == 200:
                result = response.json()
                api_logger.logger.info(f"Seller Tree API 响应成功: {result}")

                # 解析响应数据
                resolved_categories = result.get("resolved_categories_by_sku", {})
                if sku in resolved_categories:
                    category_info = resolved_categories[sku]
                    api_logger.logger.info(f"找到 SKU {sku} 的类目信息: {category_info}")
                    return category_info
                else:
                    api_logger.logger.warning(f"未找到 SKU {sku} 的类目信息")
                    return None
            else:
                api_logger.logger.error(f"Seller Tree API 请求失败: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            api_logger.logger.error(f"获取 SKU {sku} 类目信息时出错: {e}")
            return None

    def extract_sku_from_scraped_data(self, scraped_data: Dict) -> Optional[str]:
        """
        从抓取的数据中提取 SKU

        Args:
            scraped_data (Dict): 抓取的商品数据

        Returns:
            Optional[str]: 提取的 SKU，如果未找到则返回 None
        """
        # 尝试从不同字段提取 SKU
        possible_sku_fields = ['id', 'sku', 'product_id', 'ozon_id', 'article']

        for field in possible_sku_fields:
            if field in scraped_data and scraped_data[field]:
                sku = str(scraped_data[field])
                api_logger.logger.info(f"从字段 '{field}' 提取到 SKU: {sku}")
                return sku

        # 尝试从 URL 中提取 SKU
        if 'url' in scraped_data:
            url = scraped_data['url']
            import re
            # Ozon URL 格式: https://www.ozon.ru/product/name-SKU/
            match = re.search(r'-(\d+)/?(?:\?|$)', url)
            if match:
                sku = match.group(1)
                api_logger.logger.info(f"从 URL 提取到 SKU: {sku}")
                return sku

        api_logger.logger.warning("未能从抓取数据中提取到 SKU")
        return None

    def get_category_info_from_scraped_data(self, scraped_data: Dict) -> Optional[Tuple[int, int]]:
        """
        从抓取的数据中获取类目信息
        首先尝试提取 SKU，然后通过 Seller Tree API 获取准确的类目信息

        Args:
            scraped_data (Dict): 抓取的商品数据

        Returns:
            Optional[Tuple[int, int]]: (category_id, type_id) 元组，如果失败则返回 None
        """
        # 1. 尝试提取 SKU
        sku = self.extract_sku_from_scraped_data(scraped_data)

        if sku:
            # 2. 通过 Seller Tree API 获取类目信息
            category_info = self.get_category_by_sku(sku)

            if category_info:
                # 3. 解析类目信息
                # 根据诊断结果，优先使用 Level 3 类目ID，因为它是唯一有效的
                category_id = None
                type_id = category_info.get("description_type_id")

                # 按优先级选择类目ID（Level 3 优先，因为诊断显示它是有效的）
                for level in ["description_category_id_level_3", "description_category_id_level_4", "description_category_id_level_2"]:
                    if level in category_info and category_info[level]:
                        category_id = int(category_info[level])
                        api_logger.logger.info(f"选择类目级别: {level} = {category_id}")
                        break

                if category_id and type_id:
                    type_id = int(type_id)
                    api_logger.logger.info(f"通过 Seller Tree API 获取到类目信息: description_category_id={category_id}, type_id={type_id}")
                    return (category_id, type_id)
                else:
                    api_logger.logger.warning("Seller Tree API 返回的类目信息不完整")
            else:
                api_logger.logger.warning(f"Seller Tree API 未返回 SKU {sku} 的类目信息")

        # 4. 如果 Seller Tree API 失败，回退到原有的类目推断逻辑
        api_logger.logger.info("回退到原有的类目推断逻辑")
        return None
