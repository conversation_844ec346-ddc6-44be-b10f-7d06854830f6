#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
curl_cffi 专门测试工具 - 测试不同的反反爬策略
"""

import sys
import os
import time
import json
import random
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from curl_cffi import requests  # 使用 curl_cffi 替代标准 requests 以绕过反爬

class CurlCffiTester:
    """curl_cffi 测试器"""
    
    def __init__(self):
        self.test_url = "https://www.ozon.ru/product/nabor-stolovoy-posudy-shiping-iz-10-predm-kolichestvo-person-10-1727409461/"
        self.api_url = "https://www.ozon.ru/api/entrypoint-api.bx/page/json/v2?url=https%3A%2F%2Fozon.ru%2Fnotification%2Fcookies_acceptance%3Fref_page_type%3Dpdp"
        
        # 完整的Cookie（从成功的抓包请求中提取）
        self.cookies = {
            "__Secure-ab-group": "69",
            "__Secure-user-id": "*********", 
            "bacntid": "5696816",
            "sc_company_id": "2977542",
            "__Secure-ext_xcid": "f23884cb255525641dd6d1d40f14a0a3",
            "cf_clearance": "SxNHjbiUgRHM7.bAwn6UymW.9nXAj7TGfxUe.epgtaY-1753754065-*******-kVN5.Nmbp2Zdl7S9kU_gVjFopKLumsA.NulKhlayK0ixUjTNsZftVSyKI7MCg3JjFV5i.EuYOJxhstBFrymL4tYaIjL7m2vj_bOY4_sSOuahhwxNGhbBVxHtF6UFiHOeGDvR.1d4EdE6PAjUUsPCc_uBKOP08yIpcOODIK3tkZ4qyLTa.2AtEw93MiYX2o5RmybaHDms4Y4_nGrVdte2sIwSgawPsGb0OkCa3No4wW4",
            "xcid": "2e10519482eb460cccc95029b438c85e",
            "rfuid": "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",
            "__Secure-ETC": "b8a49cd83eed5e85b03a09a0483af021",
            "abt_data": "7.4levO281ub12HHVswDD3B49mj9ujBtxMpPYdAzcSiLJttcuk4bTs7dQytIqB643JazLR9NK8PosNNkU-xMO-YMTpI0CH-l4SnblGJDzxj6dE21hhGd-WZQ0I-tLFGYV2znn9aY_OaVY-jsjzM13uC8P7r7-LUXZjoyCdXp-ZPmvY3kDkkhOPiQtfQqToO0wJl7jE9xvPBgogVJ0UUGVZD9gBNVORIqOFygtQf6q4VYWB2YdTTd2GJZEPf0WXJkMZr0c8U7wHTSHpHmWOaZlP5j1uAq3V2ELHctMZpCs_FeAl9OBl-OP2HFQFuBx2YLIGp8J0tmDjrNd_tuYfKIhOrsCutr4nWjukeXWOC62mWAb1u9Rp2aZz6_X97Krc6i0gYMgrpkCtVMT8Bwk38kjDdJleMBxxhRRBVoqkX8eJhc6UWHz4rkWa1Ea6FP6P-l6uOYuhBYA7zeXHYpVeD3qp0QUX6v-0ogcY-6KW_6oay8Mqcs-I5oQATwNBmEziWaNqjBIaZ1jWGE2wukXB6Qx_2b3TBboQ93k14tCn-potckyqDFwr72k0hDPYvhRP4dDjvWdxVeLvLWqat3WhEwkTvZATbC4IdzuUnSx4FcjbXdXeYc7ThFL6sxCRhbVU4WLNTxAaiL9fZ2JDoqV6",
            "__Secure-access-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.Fq0rJlpjcr9BI2UMH-vxr6m4rRreW1AWX6rtME6Q24w.1329ce1506708804a",
            "__Secure-refresh-token": "8.*********.kU7WkzojRdiTzB_KuimFzw.69.AWs9IZp4D4JSTgutwmeJAyUlG9ZYmOKQdq4aC9Kjn9o-dSu1z6wV79-DE6HsL_fQbnuy_9hr3i4equxCaqqf1HQ-qZWU8nGpCAZQ_WFS_SpNJ5YyBQi9fGH8Icw3dOELYA.20250617125441.20250731144131.ucJ59IaYF-vy4GJOPXVPkCdX1ZOeMOEHIsNXxh6bsIE.1ce4adb5ce4c55b7d"
        }
        
        # 完整的请求头
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'priority': 'u=1, i',
            'referer': self.test_url,
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-full-version': '138.0.3351.109',
            'sec-ch-ua-full-version-list': '"Not)A;Brand";v="8.0.0.0", "Chromium";v="138.0.7204.169", "Microsoft Edge";v="138.0.3351.109"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-o3-app-name': 'dweb_client',
            'x-o3-app-version': 'release_30-6-2025_551b2394',
            'x-o3-manifest-version': 'frontend-ozon-ru:551b23941a9d92fb35bd2c7850fae943cee48228,sf-render-api:100a986a9257dfdf22e7c029f43488da3e00e497',
            'x-o3-parent-requestid': '53f00ae1cd7aebb49bff904ecfb0b03d',
            'x-page-view-id': '56537c5d-1942-47eb-7dfc-a582686a7bb1'
        }
        
        # 可用的浏览器模拟选项
        self.browser_impersonations = [
            "chrome110", "chrome107", "chrome104", "chrome101", "chrome99",
            "edge101", "edge99", "safari15_5", "safari15_3", "firefox102", "firefox91"
        ]

    def test_single_request(self, impersonation: str, use_cookies: bool = True, use_headers: bool = True) -> Dict[str, Any]:
        """测试单个请求"""
        result = {
            "impersonation": impersonation,
            "use_cookies": use_cookies,
            "use_headers": use_headers,
            "success": False,
            "status_code": None,
            "response_size": 0,
            "response_time": 0,
            "error": None,
            "has_product_data": False
        }
        
        try:
            start_time = time.time()
            
            # 准备请求参数
            cookies = self.cookies if use_cookies else {}
            headers = self.headers if use_headers else {}
            
            # 发送请求
            response = requests.get(
                self.api_url,
                cookies=cookies,
                headers=headers,
                timeout=15,
                impersonate=impersonation,
                verify=False,
                allow_redirects=True
            )
            
            result["response_time"] = time.time() - start_time
            result["status_code"] = response.status_code
            result["response_size"] = len(response.content)
            
            if response.status_code == 200:
                result["success"] = True
                
                # 检查响应是否包含商品数据
                try:
                    data = response.json()
                    if "result" in data and data["result"]:
                        result["has_product_data"] = True
                except:
                    pass
            else:
                result["error"] = f"HTTP {response.status_code}"
                
        except Exception as e:
            result["error"] = str(e)
        
        return result

    def test_all_impersonations(self):
        """测试所有浏览器模拟"""
        print("=" * 80)
        print("curl_cffi 浏览器模拟全面测试")
        print("=" * 80)
        
        results = []
        
        for impersonation in self.browser_impersonations:
            print(f"\n测试浏览器模拟: {impersonation}")
            print("-" * 50)
            
            # 测试4种组合：完整、仅Cookie、仅Headers、无Cookie无Headers
            test_cases = [
                ("完整Cookie+Headers", True, True),
                ("仅Cookie", True, False),
                ("仅Headers", False, True),
                ("无Cookie无Headers", False, False)
            ]
            
            for case_name, use_cookies, use_headers in test_cases:
                print(f"  {case_name}: ", end="")
                
                result = self.test_single_request(impersonation, use_cookies, use_headers)
                result["case_name"] = case_name
                results.append(result)
                
                if result["success"]:
                    print(f"✅ 成功 ({result['response_size']} bytes, {result['response_time']:.2f}s)")
                else:
                    print(f"❌ 失败 ({result['error']})")
                
                time.sleep(0.5)  # 避免请求过于频繁
        
        # 生成报告
        self.generate_comprehensive_report(results)
        
        return results

    def generate_comprehensive_report(self, results: List[Dict[str, Any]]):
        """生成综合报告"""
        print("\n" + "=" * 80)
        print("综合测试报告")
        print("=" * 80)
        
        # 按浏览器模拟分组
        by_impersonation = {}
        for result in results:
            imp = result["impersonation"]
            if imp not in by_impersonation:
                by_impersonation[imp] = []
            by_impersonation[imp].append(result)
        
        # 统计每个浏览器模拟的成功率
        print("浏览器模拟成功率:")
        print("-" * 50)
        
        for impersonation, imp_results in by_impersonation.items():
            successful = len([r for r in imp_results if r["success"]])
            total = len(imp_results)
            success_rate = (successful / total) * 100 if total > 0 else 0
            
            print(f"{impersonation:<15}: {successful}/{total} ({success_rate:.1f}%)")
            
            # 显示成功的组合
            successful_cases = [r for r in imp_results if r["success"]]
            if successful_cases:
                case_names = [r["case_name"] for r in successful_cases]
                print(f"                 成功组合: {', '.join(case_names)}")
        
        # 按测试用例分组
        print(f"\n测试用例成功率:")
        print("-" * 50)
        
        by_case = {}
        for result in results:
            case = result["case_name"]
            if case not in by_case:
                by_case[case] = []
            by_case[case].append(result)
        
        for case_name, case_results in by_case.items():
            successful = len([r for r in case_results if r["success"]])
            total = len(case_results)
            success_rate = (successful / total) * 100 if total > 0 else 0
            
            print(f"{case_name:<20}: {successful}/{total} ({success_rate:.1f}%)")
        
        # 最佳组合推荐
        print(f"\n最佳组合推荐:")
        print("-" * 50)
        
        successful_results = [r for r in results if r["success"]]
        if successful_results:
            # 按响应时间排序
            successful_results.sort(key=lambda x: x["response_time"])
            
            print("前5个最快的成功组合:")
            for i, result in enumerate(successful_results[:5], 1):
                print(f"{i}. {result['impersonation']} + {result['case_name']}")
                print(f"   响应时间: {result['response_time']:.2f}s, 响应大小: {result['response_size']} bytes")
        else:
            print("没有成功的组合")

    def test_specific_combination(self, impersonation: str, use_cookies: bool = True, use_headers: bool = True):
        """测试特定组合"""
        print(f"测试组合: {impersonation} + Cookie:{use_cookies} + Headers:{use_headers}")
        print("-" * 60)
        
        result = self.test_single_request(impersonation, use_cookies, use_headers)
        
        print(f"结果: {'✅ 成功' if result['success'] else f'❌ 失败'}")
        print(f"状态码: {result['status_code']}")
        print(f"响应大小: {result['response_size']} bytes")
        print(f"响应时间: {result['response_time']:.2f}s")
        print(f"包含商品数据: {'是' if result['has_product_data'] else '否'}")
        
        if result['error']:
            print(f"错误信息: {result['error']}")
        
        return result

    def find_working_combinations(self):
        """快速找到可工作的组合"""
        print("=" * 80)
        print("快速寻找可工作的组合")
        print("=" * 80)
        
        working_combinations = []
        
        # 优先测试最常用的浏览器模拟
        priority_impersonations = ["chrome110", "chrome107", "edge101", "safari15_5"]
        
        for impersonation in priority_impersonations:
            print(f"测试 {impersonation}...")
            
            # 首先测试完整组合
            result = self.test_single_request(impersonation, True, True)
            if result["success"]:
                working_combinations.append((impersonation, "完整Cookie+Headers"))
                print(f"  ✅ 完整组合成功")
                continue
            
            # 如果完整组合失败，测试其他组合
            test_cases = [
                ("仅Cookie", True, False),
                ("仅Headers", False, True),
                ("无Cookie无Headers", False, False)
            ]
            
            found_working = False
            for case_name, use_cookies, use_headers in test_cases:
                result = self.test_single_request(impersonation, use_cookies, use_headers)
                if result["success"]:
                    working_combinations.append((impersonation, case_name))
                    print(f"  ✅ {case_name} 成功")
                    found_working = True
                    break
            
            if not found_working:
                print(f"  ❌ 所有组合都失败")
            
            time.sleep(1)
        
        print(f"\n找到 {len(working_combinations)} 个可工作的组合:")
        for i, (imp, case) in enumerate(working_combinations, 1):
            print(f"{i}. {imp} + {case}")
        
        return working_combinations

def main():
    """主程序"""
    tester = CurlCffiTester()
    
    print("curl_cffi 反反爬测试工具")
    print("1. 测试所有浏览器模拟")
    print("2. 快速寻找可工作组合")
    print("3. 测试特定组合")
    print("4. 显示可用浏览器模拟")
    
    choice = input("请选择操作 (1-4): ").strip()
    
    if choice == "1":
        tester.test_all_impersonations()
    elif choice == "2":
        tester.find_working_combinations()
    elif choice == "3":
        print("\n可用的浏览器模拟:")
        for i, imp in enumerate(tester.browser_impersonations, 1):
            print(f"  {i}. {imp}")
        
        imp_choice = input("请输入浏览器模拟名称: ").strip()
        use_cookies = input("使用Cookie? (y/N): ").strip().lower() == 'y'
        use_headers = input("使用Headers? (y/N): ").strip().lower() == 'y'
        
        tester.test_specific_combination(imp_choice, use_cookies, use_headers)
    elif choice == "4":
        print("\n可用的浏览器模拟:")
        for i, imp in enumerate(tester.browser_impersonations, 1):
            print(f"  {i}. {imp}")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
