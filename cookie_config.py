#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie 配置文件
提供简化的 Cookie 管理接口
"""

import os
from dotenv import load_dotenv
from modules.cookie_manager import get_api_cookie, get_html_cookie, validate_cookie

# 加载环境变量
load_dotenv()

class CookieConfig:
    """Cookie 配置管理类"""
    
    def __init__(self):
        """初始化 Cookie 配置"""
        self.full_cookie = os.getenv("OZON_SESSION_COOKIE", "")
        self._api_cookie = None
        self._html_cookie = None
        self._is_validated = False
    
    @property
    def api_cookie(self) -> str:
        """获取 API 请求用的最小化 Cookie"""
        if self._api_cookie is None:
            self._api_cookie = get_api_cookie(self.full_cookie)
        return self._api_cookie
    
    @property
    def html_cookie(self) -> str:
        """获取 HTML 请求用的 Cookie"""
        if self._html_cookie is None:
            self._html_cookie = get_html_cookie(self.full_cookie)
        return self._html_cookie
    
    @property
    def is_valid(self) -> bool:
        """检查 Cookie 是否有效"""
        if not self._is_validated:
            self._is_validated = validate_cookie(self.full_cookie)
        return self._is_validated
    
    def refresh(self):
        """刷新 Cookie 缓存"""
        self.full_cookie = os.getenv("OZON_SESSION_COOKIE", "")
        self._api_cookie = None
        self._html_cookie = None
        self._is_validated = False
    
    def update_cookie(self, new_cookie: str) -> bool:
        """
        更新 Cookie
        
        Args:
            new_cookie (str): 新的 Cookie 字符串
            
        Returns:
            bool: 更新是否成功
        """
        if validate_cookie(new_cookie):
            self.full_cookie = new_cookie
            self._api_cookie = None
            self._html_cookie = None
            self._is_validated = False
            return True
        return False
    
    def get_cookie_info(self) -> dict:
        """
        获取 Cookie 信息
        
        Returns:
            dict: Cookie 信息字典
        """
        return {
            "full_cookie_length": len(self.full_cookie),
            "api_cookie_length": len(self.api_cookie),
            "html_cookie_length": len(self.html_cookie),
            "is_valid": self.is_valid,
            "has_abt_data": "abt_data=" in self.full_cookie
        }

# 全局 Cookie 配置实例
cookie_config = CookieConfig()

# 便捷函数
def get_api_cookie_simple() -> str:
    """获取 API Cookie 的简化接口"""
    return cookie_config.api_cookie

def get_html_cookie_simple() -> str:
    """获取 HTML Cookie 的简化接口"""
    return cookie_config.html_cookie

def is_cookie_valid() -> bool:
    """检查 Cookie 是否有效的简化接口"""
    return cookie_config.is_valid

def refresh_cookie():
    """刷新 Cookie 的简化接口"""
    cookie_config.refresh()

# 常用的 abt_data 值（示例，实际使用时需要替换）
SAMPLE_ABT_DATA = "7.sample_abt_data_value_here"

# Cookie 模板
MINIMAL_COOKIE_TEMPLATE = "abt_data={abt_data_value}"

def create_minimal_cookie_from_abt_data(abt_data_value: str) -> str:
    """
    从 abt_data 值创建最小化 Cookie
    
    Args:
        abt_data_value (str): abt_data 的值
        
    Returns:
        str: 最小化的 Cookie 字符串
    """
    return MINIMAL_COOKIE_TEMPLATE.format(abt_data_value=abt_data_value)

# 使用示例
if __name__ == "__main__":
    print("=== Cookie 配置测试 ===")
    
    # 显示 Cookie 信息
    info = cookie_config.get_cookie_info()
    print(f"Cookie 信息: {info}")
    
    # 测试各种 Cookie
    print(f"API Cookie: {get_api_cookie_simple()[:50]}...")
    print(f"HTML Cookie: {get_html_cookie_simple()[:50]}...")
    print(f"Cookie 有效性: {is_cookie_valid()}")
    
    # 测试最小化 Cookie 创建
    sample_minimal = create_minimal_cookie_from_abt_data(SAMPLE_ABT_DATA)
    print(f"示例最小化 Cookie: {sample_minimal}")
