#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查品牌字典值的程序
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.api_client import OzonApiClient
import config

def check_brand_dictionary(api_client: OzonApiClient):
    """检查品牌字典中的实际值"""
    try:
        # 使用已知的类目信息
        description_category_id = 53968796  # Level 3 类目ID
        type_id = 971080300
        brand_attribute_id = 85  # 品牌属性ID
        
        print(f"获取品牌属性字典值...")
        print(f"description_category_id: {description_category_id}")
        print(f"type_id: {type_id}")
        print(f"attribute_id: {brand_attribute_id}")
        
        brand_values = api_client.get_attribute_values(
            description_category_id=description_category_id,
            attribute_id=brand_attribute_id,
            type_id=type_id
        )
        
        if not brand_values:
            print("未获取到品牌字典值")
            return
        
        print(f"获取到 {len(brand_values)} 个品牌值")
        
        # 查找"无品牌"相关的值
        no_brand_keywords = ['без', 'нет', 'no', 'none', 'бренд', 'brand']
        no_brand_candidates = []
        
        print(f"\n查找'无品牌'相关的值...")
        for value in brand_values:
            value_text = value.get('value', '').lower()
            for keyword in no_brand_keywords:
                if keyword in value_text:
                    no_brand_candidates.append(value)
                    break
        
        if no_brand_candidates:
            print(f"找到 {len(no_brand_candidates)} 个可能的'无品牌'选项:")
            for i, value in enumerate(no_brand_candidates, 1):
                print(f"  {i}. '{value.get('value')}' (ID: {value.get('id')})")
        else:
            print("未找到明确的'无品牌'选项")
        
        # 显示前20个品牌值
        print(f"\n前20个品牌值:")
        for i, value in enumerate(brand_values[:20], 1):
            print(f"  {i}. '{value.get('value')}' (ID: {value.get('id')})")
        
        # 查找一些常见品牌
        common_brands = ['Apple', 'Samsung', 'Xiaomi', 'Huawei', 'Sony', 'LG', 'Philips']
        print(f"\n查找常见品牌:")
        for brand in common_brands:
            found = False
            for value in brand_values:
                if brand.lower() in value.get('value', '').lower():
                    print(f"  {brand}: 找到 '{value.get('value')}' (ID: {value.get('id')})")
                    found = True
                    break
            if not found:
                print(f"  {brand}: 未找到")
        
        # 测试匹配函数
        print(f"\n测试匹配函数:")
        test_values = ['Без бренда', 'Нет бренда', 'No brand', 'Apple', 'Samsung']
        
        for test_value in test_values:
            matched_id = api_client.match_attribute_value(brand_values, test_value)
            if matched_id:
                # 找到匹配的值
                matched_value = next((v for v in brand_values if v.get('id') == matched_id), None)
                print(f"  '{test_value}' -> 匹配到: '{matched_value.get('value')}' (ID: {matched_id})")
            else:
                print(f"  '{test_value}' -> 未找到匹配")
        
        return brand_values
        
    except Exception as e:
        print(f"检查品牌字典失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主程序"""
    try:
        print("验证配置...")
        config.validate_config()
        
        print("初始化API客户端...")
        api_client = OzonApiClient(
            client_id=config.OZON_CLIENT_ID,
            api_key=config.OZON_API_KEY,
            base_url=config.API_BASE_URL
        )
        
        print("测试API连接...")
        if not api_client.test_api_connection():
            print("API连接失败")
            return
        
        print("API连接成功")
        
        # 检查品牌字典
        brand_values = check_brand_dictionary(api_client)
        
        if brand_values:
            print(f"\n建议:")
            print(f"1. 如果找到了'无品牌'选项，使用其确切的文本和ID")
            print(f"2. 如果没有'无品牌'选项，可以选择一个通用品牌或创建自定义品牌")
            print(f"3. 修改 find_matching_attribute_value 函数，返回正确的品牌值")
        
        print(f"\n检查完成!")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
