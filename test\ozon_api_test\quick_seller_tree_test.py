#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 Ozon Seller Tree API
基于提供的 curl 命令的简化版本测试脚本
"""

import json
from curl_cffi import requests

def test_ozon_seller_tree_api():
    """快速测试 Ozon Seller Tree API"""
    
    # API 端点
    url = "https://seller.ozon.ru/api/v1/seller-tree/resolve/by-sku"
    
    # 请求头（从 curl 命令复制）
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'zh-Hans',
        'content-type': 'application/json',
        'origin': 'https://seller.ozon.ru',
        'priority': 'u=1, i',
        'referer': 'https://seller.ozon.ru/app/products/add/general-info',
        'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
        'x-o3-app-name': 'seller-ui',
        'x-o3-company-id': '2977542',
        'x-o3-language': 'zh-Hans',
        'x-o3-page-type': 'products-other'
    }
    
    # Cookie（从 curl 命令复制）
    cookie = '__Secure-ab-group=72; __Secure-ext_xcid=27ca9db73a2885b37934c4130606a414; sc_company_id=2977542; __Secure-user-id=*********; bacntid=5696816; xcid=3b14ce650cfc9be64745fdb9568f961b; __Secure-access-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.yE60b99xGsn2J2f3HABVAblLp7spsUv9N-yLpw7RWPI.13bf208a7ad6f9e10; __Secure-refresh-token=8.*********.yyb8s2M0Qn2-eWOePBUtuw.72.Ad6VarYluCNcP8zgOpbH1AOs9mY-eLuLOin5TLU4RpAGrm4abH9CiqiSfcjf7KPGzEa-kPdVCiewXLMkPCkUyxidpYieXto0dSIsX7ViouMiXirXFwg5PIk0a6HcaeSV3A.20250617125441.20250726143454.FMm6Tul80XcJPDGioCF3qI2s3B-cb3klHs5zB8qsTa8.1e6b86f6a6dbe269c; x-o3-language=zh-Hans; rfuid=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; __Secure-ETC=a79631568bd4d9011cf502138c89ec98; abt_data=7._SMbImnoivov7DkMARlIu1R2yi5a-UIVlM4XDvkxHAweKYYVA-037SwT7EkbUVxtdpH2Idmbeah5Ml7dz3gMCtQUy3Qal00h1HmI-XxCOGbrqgQUF4Cpf21l8DjdGNbiUAU3xqbXEaG-5eJ0vnc71Na2T1vZtA7WKb3brekO0xJ2UmU6pINA_hwAhWIfYr_A5jGVwCdpG7XGkQPyzgS5mqAwDtaqylcgk_sDXwI9GAJT2QpdlMkoy5wUJYQ1Sa_HlltbpRvOZezrdWnbebN4brjzvis0r8TgajipH3GIFqx6kHsFpDJj0MqnXMvPT9SnuhEOE5bZIC3hVysKGJlxbGvDiyIzfS9fznxG5m5uAWSIpGaKRADrvdC_HfFFhdnM4z2jEqWxlZtSEz7fPtFkkJ4z8czxXVTDSSqBcUJtq6CsVHoC852t57ZQ98WSW0GOjV_w2GBXgUBatW_LRQiB5R9tA6_LxHhBYh89MZYVjaSqkVsZUcq5H1BpHmHGO4earXXBYS19DNH4BABf-Wk6wxA-pqy1xH_xczl_YH8TTIPffs44yC8Y0QNXR1gv69G0iksQyRtcP_lrTwUSrR8kT2a1VyX8ZBElCL7RAp2-srJifjqC5okfuXvOnas6RIycw9OL1sJ-x9JzhTv9-6Y'
    
    headers['Cookie'] = cookie
    
    # 请求数据（从 curl 命令复制）
    data = {"skus": ["2045588660"]}
    
    print("🚀 开始测试 Ozon Seller Tree API...")
    print(f"URL: {url}")
    print(f"数据: {json.dumps(data, ensure_ascii=False)}")
    
    try:
        # 使用 curl_cffi 发送请求
        response = requests.post(
            url,
            headers=headers,
            json=data,
            timeout=30,
            impersonate="chrome110",  # 模拟 Chrome 浏览器指纹
            verify=True
        )
        
        print(f"\n📊 响应结果:")
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 请求成功!")
            try:
                result = response.json()
                print(f"响应数据:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
            except json.JSONDecodeError:
                print(f"响应内容 (非JSON): {response.text}")
        else:
            print(f"❌ 请求失败 (状态码: {response.status_code})")
            print(f"错误内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_ozon_seller_tree_api()
