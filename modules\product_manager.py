# modules/product_manager.py
"""
商品管理模块 - 提供商品的完整生命周期管理功能
"""

from typing import List, Dict, Optional, Tuple
from .api_client import OzonApiClient
import time
import json

class ProductManager:
    """商品管理器 - 提供商品的增删改查和状态管理功能"""
    
    def __init__(self, api_client: OzonApiClient):
        self.api_client = api_client
    
    def create_product_with_monitoring(self, product_data: dict, wait_for_completion: bool = True) -> dict:
        """
        创建商品并监控状态
        
        Args:
            product_data: 商品数据
            wait_for_completion: 是否等待创建完成
            
        Returns:
            创建结果和状态信息
        """
        print("🚀 开始创建商品...")
        
        try:
            # 使用v3 API创建商品（官方推荐）
            response = self.api_client.create_product([product_data])
            task_id = response.get("result", {}).get("task_id")
            
            if not task_id:
                return {"success": False, "error": "未获取到任务ID", "response": response}
            
            print(f"✅ 商品创建请求已提交，任务ID: {task_id}")
            
            result = {
                "success": True,
                "task_id": task_id,
                "response": response
            }
            
            if wait_for_completion:
                print("⏳ 等待商品创建完成...")
                success, final_status = self.api_client.wait_for_import_completion(task_id)
                result.update({
                    "creation_success": success,
                    "final_status": final_status
                })
                
                if success:
                    items = final_status.get("items", [])
                    if items:
                        item_info = items[0]
                        product_id = item_info.get("product_id")
                        if product_id:
                            result["product_id"] = product_id
                            print(f"🎉 商品创建成功！商品ID: {product_id}")
                        else:
                            print("⚠️ 商品创建完成，但未获取到商品ID")
                else:
                    print("❌ 商品创建失败")
                    errors = final_status.get("items", [{}])[0].get("errors", [])
                    result["errors"] = errors
            
            return result
            
        except Exception as e:
            print(f"❌ 创建商品时出错: {e}")
            return {"success": False, "error": str(e)}
    
    def get_product_status(self, product_id: int = None, offer_id: str = None) -> Optional[dict]:
        """
        获取商品状态信息
        
        Args:
            product_id: 商品ID
            offer_id: 商品SKU
            
        Returns:
            商品状态信息
        """
        try:
            if product_id:
                response = self.api_client.get_product_info_list(product_ids=[product_id])
            elif offer_id:
                response = self.api_client.get_product_info_list(offer_ids=[offer_id])
            else:
                return None
            
            items = response.get("result", {}).get("items", [])
            return items[0] if items else None
            
        except Exception as e:
            print(f"获取商品状态失败: {e}")
            return None
    
    def update_product_price(self, product_id: int, new_price: float, old_price: float = None) -> bool:
        """
        更新商品价格
        
        Args:
            product_id: 商品ID
            new_price: 新价格
            old_price: 原价（可选）
            
        Returns:
            是否更新成功
        """
        try:
            price_data = {
                "product_id": product_id,
                "price": str(new_price)
            }
            
            if old_price:
                price_data["old_price"] = str(old_price)
            
            response = self.api_client.update_product_prices([price_data])
            
            # 检查更新结果
            result = response.get("result", {})
            if result.get("updated"):
                print(f"✅ 商品 {product_id} 价格更新成功: {new_price}")
                return True
            else:
                print(f"❌ 商品 {product_id} 价格更新失败")
                return False
                
        except Exception as e:
            print(f"更新商品价格失败: {e}")
            return False
    
    def update_product_stock(self, product_id: int, stock_quantity: int, warehouse_id: int = None) -> bool:
        """
        更新商品库存
        
        Args:
            product_id: 商品ID
            stock_quantity: 库存数量
            warehouse_id: 仓库ID（可选）
            
        Returns:
            是否更新成功
        """
        try:
            stock_data = {
                "product_id": product_id,
                "stock": stock_quantity
            }
            
            if warehouse_id:
                stock_data["warehouse_id"] = warehouse_id
            
            response = self.api_client.update_product_stocks([stock_data])
            
            # 检查更新结果
            result = response.get("result", {})
            if result.get("updated"):
                print(f"✅ 商品 {product_id} 库存更新成功: {stock_quantity}")
                return True
            else:
                print(f"❌ 商品 {product_id} 库存更新失败")
                return False
                
        except Exception as e:
            print(f"更新商品库存失败: {e}")
            return False
    
    def archive_products(self, product_ids: List[int]) -> dict:
        """
        批量存档商品
        
        Args:
            product_ids: 商品ID列表
            
        Returns:
            存档结果
        """
        try:
            response = self.api_client.archive_product(product_ids)
            print(f"✅ 已存档 {len(product_ids)} 个商品")
            return response
        except Exception as e:
            print(f"存档商品失败: {e}")
            return {"success": False, "error": str(e)}
    
    def unarchive_products(self, product_ids: List[int]) -> dict:
        """
        批量恢复商品
        
        Args:
            product_ids: 商品ID列表
            
        Returns:
            恢复结果
        """
        try:
            response = self.api_client.unarchive_product(product_ids)
            print(f"✅ 已恢复 {len(product_ids)} 个商品")
            return response
        except Exception as e:
            print(f"恢复商品失败: {e}")
            return {"success": False, "error": str(e)}
    
    def delete_products(self, products: List[dict]) -> dict:
        """
        批量删除商品
        
        Args:
            products: 商品信息列表，格式: [{"offer_id": "SKU123"}, ...]
            
        Returns:
            删除结果
        """
        try:
            response = self.api_client.delete_product(products)
            print(f"✅ 已删除 {len(products)} 个商品")
            return response
        except Exception as e:
            print(f"删除商品失败: {e}")
            return {"success": False, "error": str(e)}
    
    def get_product_list_with_filters(self, filters: dict = None, limit: int = 100) -> List[dict]:
        """
        获取商品列表（支持过滤）
        
        Args:
            filters: 过滤条件
            limit: 返回数量限制
            
        Returns:
            商品列表
        """
        try:
            response = self.api_client.get_product_list(filters, limit)
            items = response.get("result", {}).get("items", [])
            print(f"📦 获取到 {len(items)} 个商品")
            return items
        except Exception as e:
            print(f"获取商品列表失败: {e}")
            return []
    
    def get_product_performance_report(self, product_ids: List[int] = None) -> dict:
        """
        获取商品表现报告
        
        Args:
            product_ids: 商品ID列表（可选）
            
        Returns:
            商品表现数据
        """
        try:
            filter_params = {}
            if product_ids:
                filter_params["product_id"] = product_ids
            
            response = self.api_client.create_report("products", filter_params)
            code = response.get("result", {}).get("code")
            
            if code:
                # 等待报告生成
                time.sleep(5)
                report_info = self.api_client.get_report_info(code)
                return report_info
            
            return response
        except Exception as e:
            print(f"获取商品报告失败: {e}")
            return {"success": False, "error": str(e)}
    
    def bulk_update_attributes(self, updates: List[dict]) -> dict:
        """
        批量更新商品属性
        
        Args:
            updates: 更新数据列表
            
        Returns:
            更新结果
        """
        try:
            response = self.api_client.update_product_attributes(updates)
            print(f"✅ 已更新 {len(updates)} 个商品的属性")
            return response
        except Exception as e:
            print(f"批量更新属性失败: {e}")
            return {"success": False, "error": str(e)}
