# Ozon Seller Tree API 测试工具

这个目录包含了用于测试 Ozon Seller Tree API 的多个 Python 脚本，基于您提供的 curl 命令创建，使用 `curl_cffi.requests` 库来模拟真实浏览器请求。

## 📁 文件说明

### 核心测试文件

1. **`quick_seller_tree_test.py`** - 快速测试脚本
   - 最简单的测试脚本，直接复制 curl 命令的参数
   - 适合快速验证 API 是否可用

2. **`test_ozon_seller_tree_api.py`** - 完整测试脚本
   - 包含多种测试方法和场景
   - 支持不同 SKU 组合测试
   - 自动保存测试结果

3. **`advanced_seller_tree_test.py`** - 高级测试脚本
   - 使用配置文件管理参数
   - 支持多种测试场景和浏览器指纹
   - 详细的测试报告和结果分析

### 配置文件

4. **`test_config.py`** - 测试配置文件
   - 集中管理所有测试参数
   - 包含 API 配置、请求头、Cookie 等
   - 定义多种测试场景

## 🚀 快速开始

### 1. 安装依赖

确保已安装必要的依赖：

```bash
pip install curl-cffi requests
```

### 2. 运行快速测试

```bash
cd test
python quick_seller_tree_test.py
```

### 3. 运行完整测试

```bash
python test_ozon_seller_tree_api.py
```

### 4. 运行高级测试

```bash
# 运行基本测试
python advanced_seller_tree_test.py

# 运行所有测试场景
python advanced_seller_tree_test.py all

# 测试不同浏览器指纹
python advanced_seller_tree_test.py fingerprints

# 运行特定场景
python advanced_seller_tree_test.py basic
python advanced_seller_tree_test.py multiple_skus
python advanced_seller_tree_test.py empty_skus
python advanced_seller_tree_test.py invalid_skus
```

## 📋 测试场景

### 基本场景
- **basic**: 使用原始 SKU (2045588660) 的基本请求
- **multiple_skus**: 测试多个 SKU 的请求
- **empty_skus**: 测试空 SKU 列表
- **invalid_skus**: 测试无效 SKU

### 浏览器指纹测试
- Chrome 124
- Chrome 110  
- Edge 99
- Safari 15.5

## 🔧 配置说明

### API 配置
```python
API_CONFIG = {
    "base_url": "https://seller.ozon.ru",
    "seller_tree_endpoint": "/api/v1/seller-tree/resolve/by-sku",
    "timeout": 30,
    "impersonate": "chrome124"
}
```

### 请求头配置
脚本使用了完整的浏览器请求头，包括：
- User-Agent
- Accept headers
- Security headers (sec-ch-ua, sec-fetch-*)
- Ozon 特定头部 (x-o3-*)

### Cookie 配置
使用了您提供的完整 Cookie 字符串，包含：
- 认证令牌 (__Secure-access-token)
- 用户 ID (__Secure-user-id)
- 公司 ID (sc_company_id)
- 其他会话信息

## 📊 测试结果

### 输出格式
测试结果包含：
- 请求状态码
- 响应头信息
- 响应数据 (JSON 格式)
- 错误信息 (如果有)
- 执行时间

### 结果保存
- 自动保存到 `test/results/` 目录
- JSON 格式，包含完整的请求和响应信息
- 文件名包含时间戳

## ⚠️ 注意事项

### Cookie 有效性
- 提供的 Cookie 可能有时效性
- 如果收到 401/403 错误，可能需要更新 Cookie
- 可以从浏览器开发者工具获取最新 Cookie

### 请求频率
- 避免过于频繁的请求
- Ozon 可能有速率限制
- 建议在测试间添加适当延迟

### 安全考虑
- Cookie 包含敏感信息，请勿分享
- 仅在测试环境使用
- 定期更新认证信息

## 🛠️ 自定义测试

### 添加新的 SKU
在 `test_config.py` 中修改 `TEST_SKUS`：

```python
TEST_SKUS = {
    "your_test": ["your_sku_1", "your_sku_2"],
    # ...
}
```

### 添加新的测试场景
在 `test_config.py` 中添加到 `TEST_SCENARIOS`：

```python
TEST_SCENARIOS = {
    "your_scenario": {
        "description": "您的测试描述",
        "skus": TEST_SKUS["your_test"],
        "expected_status": [200, 400]
    },
    # ...
}
```

### 更新 Cookie
替换 `test_config.py` 中的 `OZON_COOKIE` 变量。

## 🐛 故障排除

### 常见错误

1. **403 Forbidden**
   - Cookie 可能已过期
   - 需要更新认证信息

2. **连接超时**
   - 检查网络连接
   - 增加超时时间

3. **导入错误**
   - 确保安装了 curl-cffi
   - 检查 Python 路径

### 调试技巧
- 使用 `-v` 参数查看详细输出
- 检查 `test/results/` 目录中的详细日志
- 比较成功和失败请求的差异

## 📞 支持

如果遇到问题：
1. 检查 Cookie 是否有效
2. 确认网络连接正常
3. 查看详细的错误日志
4. 尝试不同的浏览器指纹
