# Ozon Seller API 方法文档

生成时间: 2025-07-23 21:41:15

根据Ozon官方文档中的SELLER API方法分类整理

## 📊 统计信息

- **总接口数**: 149
- **分类数**: 21

## 📋 分类目录

1. **商品上传和更新** (23 个接口)
2. **商品条形码列表** (2 个接口)
3. **商品价格和库存** (7 个接口)
4. **促销活动** (8 个接口)
5. **定价策略** (12 个接口)
6. **仓库** (2 个接口)
7. **多边形** (2 个接口)
8. **FBS/rFBS标志代码和营销管理** (7 个接口)
9. **FBS配送** (2 个接口)
10. **rFBS配送** (6 个接口)
11. **通行证** (8 个接口)
12. **FBO和rFBS商品集装** (13 个接口)
13. **退货** (1 个接口)
14. **取消订单** (7 个接口)
15. **rFBS商品集装** (8 个接口)
16. **与买家的聊天** (7 个接口)
17. **报告** (7 个接口)
18. **分析报告** (3 个接口)
19. **财务报告** (7 个接口)
20. **测试** (10 个接口)
21. **评价** (7 个接口)

## 📖 接口详情

### 🔹 商品上传和更新

#### 1. 商品类别和类型的树形图

- **路径**: `/v1/description-category/tree`
- **方法**: `POST`
- **描述**: 返回商品的类别和类型的树形图。  只有在最后级别的类别中可以创建商品，请对比它们，是否与平台上的类别相符合。  类别不会根据用户请求而创建。     请慎重为商品选择类别：不同的类别会有不同的佣金。 
- **操作ID**: `DescriptionCategoryAPI_GetTree`

#### 2. 类别特征列表

- **路径**: `/v1/description-category/attribute`
- **方法**: `POST`
- **描述**: 获取指定类别和类型的商品特征。  如果`dictionary_id` 的值为`0`，则该属性没有嵌套指南。 如果值不同，还有指南。使用[/v1/description-category/attribute/values](#operation/DescriptionCategoryAPI_GetAt...
- **操作ID**: `DescriptionCategoryAPI_GetAttributes`

#### 3. 特征值指南

- **路径**: `/v1/description-category/attribute/values`
- **方法**: `POST`
- **描述**: 返回特征值指南。  通过方法[/v1/description-category/attribute](#operation/DescriptionCategoryAPI_GetAttributes)可以查询是否存在嵌套指南。
- **操作ID**: `DescriptionCategoryAPI_GetAttributeValues`

#### 4. 根据属性的参考值进行搜索

- **路径**: `/v1/description-category/attribute/values/search`
- **方法**: `POST`
- **描述**: 根据请求中的指定值`value`返回属性的参考值。  是否存在嵌套参考表，可以通过方法 [/v1/description-category/attribute](#operation/DescriptionCategoryAPI_GetAttributes) 进行查询。
- **操作ID**: `DescriptionCategoryAPI_SearchAttributeValues`

#### 5. 创建或更新商品

- **路径**: `/v3/product/import`
- **方法**: `POST`
- **描述**:  如果您按照FBP工作模式从中国或香港销售，那么请为每件商品生成条形码。合作伙伴仓库将无法接收没有条形码的商品。   创建商品并更新有关商品信息的方法。  欲知限制，请使用 [/v4/product/info/limit](#operation/ProductAPI_GetUploadQuota)。...
- **操作ID**: `ProductAPI_ImportProductsV3`

#### 6. 查询商品添加或更新状态

- **路径**: `/v1/product/import/info`
- **方法**: `POST`
- **描述**: 允许获取商品卡片创建或更新的状态。
- **操作ID**: `ProductAPI_GetImportProductsInfo`

#### 7. 通过SKU创建商品

- **路径**: `/v1/product/import-by-sku`
- **方法**: `POST`
- **描述**: 该方法会创建指定SKU的商品卡片副本。 如果卖家[禁止复制](https://docs.ozon.ru/global/zh-hans/products/upload/upload-types/copying/?country=CN)， 将无法创建卡片副本。  无法通过SKU更新商品。
- **操作ID**: `ProductAPI_ImportProductsBySKU`

#### 8. 更新商品特征

- **路径**: `/v1/product/attributes/update`
- **方法**: `POST`
- **操作ID**: `ProductAPI_ProductUpdateAttributes`

#### 9. 上传或更新商品图片

- **路径**: `/v1/product/pictures/import`
- **方法**: `POST`
- **描述**: 上传或更新商品图像的方法。  每次调用该方法时，要传递所有应该出现在商品详情页上的图片。例如，如果您调用该方法并上传了10张图片，然后再次调用该方法并上传了另一张。 那么之前的10账都将被删除。  要上传，请将图像的链接地址传到公共云存储。 链接图片的格式是JPG或PNG。  根据网站上所需的顺序排...
- **操作ID**: `ProductAPI_ProductImportPictures`

#### 10. 品列表的

- **路径**: `/v3/product/list`
- **方法**: `POST`
- **描述**: 用于获取所有商品列表的方法。  如果使用 `offer_id` 或 `product_id` 进行筛选，则无需填写其他参数。 每次请求只能使用一组标识符，且商品数量不能超过 1000 个。  如果不使用标识符进行查询，则需在后续请求中指定 `limit` 和 `last_id`。
- **操作ID**: `ProductAPI_GetProductList`

#### 11. 按SKU获得商品的内容排名

- **路径**: `/v1/product/rating-by-sku`
- **方法**: `POST`
- **描述**: 一种获得商品内容排名的方法，以及如何提高排名的建议。  [与内容排行有关的更多详细信息](https://docs.ozon.ru/global/zh-hans/products/selling-pdp/content-rating/)
- **操作ID**: `ProductAPI_GetProductRatingBySku`

#### 12. 根据标识符获取商品信息

- **路径**: `/v3/product/info/list`
- **方法**: `POST`
- **描述**: 用于根据商品标识符获取商品信息的方法。  请求体应包含同类型标识符的数组，响应中将返回 `items` 数组。  单个请求最多可通过 `offer_id`、`product_id` 和 `sku` 传递总计不超过 1000个商品标识符。
- **操作ID**: `ProductAPI_GetProductInfoList`

#### 13. 获取商品特征描述

- **路径**: `/v3/products/info/attributes`
- **方法**: `POST`
- **描述**:    该方法已过时。请切换到新版本 /v4/product/info/attributes。   按其识别码退回商品特征的描述。该商品可以通过`offer_id`或`product_id` 搜索。
- **操作ID**: `ProductAPI_GetProductAttributesV3`

#### 14. 获取商品特征描述

- **路径**: `/v4/product/info/attributes`
- **方法**: `POST`
- **描述**: 按其识别码退回商品特征的描述。该商品可以通过`offer_id`, `product_id`或`sku`搜索。
- **操作ID**: `ProductAPI_GetProductAttributesV4`

#### 15. 获取商品详细信息

- **路径**: `/v1/product/info/description`
- **方法**: `POST`
- **操作ID**: `ProductAPI_GetProductInfoDescription`

#### 16. 品类限制、商品的创建和更新

- **路径**: `/v4/product/info/limit`
- **方法**: `POST`
- **描述**: 获取限制信息的方式： - 品类 —  在您的个人中心可创建多少商品。 - 创建商品 — 一天可以创建多少商品。 - 商品更新 — 一天可以修改多少商品。  如果您有品类限制并将其用完，您将无法创建新商品。
- **操作ID**: `ProductAPI_GetUploadQuota`

#### 17. 从卖家的系统中改变商品货号

- **路径**: `/v1/product/update/offer-id`
- **方法**: `POST`
- **描述**: 改变附加在商品上的 `offer_id`的方法。您可以改变几个 `offer_id`。  建议在一个数组中最多提交250个值。
- **操作ID**: `ProductAPI_ProductUpdateOfferID`

#### 18. 将商品归档

- **路径**: `/v1/product/archive`
- **方法**: `POST`
- **操作ID**: `ProductAPI_ProductArchive`

#### 19. 从档案中还原商品

- **路径**: `/v1/product/unarchive`
- **方法**: `POST`
- **描述**: 该方法适用于中国的卖家。
- **操作ID**: `ProductAPI_ProductUnarchive`

#### 20. 从存档删除没有SKU的商品

- **路径**: `/v2/products/delete`
- **方法**: `POST`
- **描述**: 在一次请求中最多可以提交500个识别码。
- **操作ID**: `ProductAPI_DeleteProducts`

#### 21. 订阅该商品的用户数

- **路径**: `/v1/product/info/subscription`
- **方法**: `POST`
- **描述**: 获取在商品页面上单击 **获取参与详情** 用户数量的方法。  您可以在一个请求中提交多个商品。
- **操作ID**: `ProductAPI_GetProductInfoSubscription`

#### 22. 获取相关SKU

- **路径**: `/v1/product/related-sku/get`
- **方法**: `POST`
- **描述**: 用于通过旧的SKU FBS和SKU FBO标识符获取统一SKU的方法。 响应中将包含所有与传递的SKU相关的SKU。  该方法可以处理任何SKU，包括隐藏的或已删除的SKU。  在一个请求中最多传递200个SKU。
- **操作ID**: `ProductAPI_ProductGetRelatedSKU`

#### 23. 获取商品图片

- **路径**: `/v2/product/pictures/info`
- **方法**: `POST`
- **操作ID**: `ProductAPI_ProductInfoPicturesV2`

---

### 🔹 商品条形码列表

#### 1. 为商品绑定条形码

- **路径**: `/v1/barcode/add`
- **方法**: `POST`
- **描述**: 如果商品已有条形码但未在 Ozon 系统中登记，可通过此方法绑定。 如果没有条形码，您可以通过 [/v1/barcode/generate](#operation/generate-barcode) 方法生成。  每次请求最多可为 100 个商品分配条形码。 每个商品最多可绑定 100 个条形码。 ...
- **操作ID**: `add-barcode`

#### 2. 创建商品条形码

- **路径**: `/v1/barcode/generate`
- **方法**: `POST`
- **描述**: 如果商品没有条形码，您可以通过此方法生成条形码。 如果商品已有条形码但未在 Ozon 系统中登记，您可以通过 [/v1/barcode/add](#operation/add-barcode) 方法进行绑定。  每次请求最多可为 100 个商品生成条形码。 每个卖家账号每分钟最多可使用该方法 20 ...
- **操作ID**: `generate-barcode`

---

### 🔹 商品价格和库存

#### 1. 更新库存商品的数量

- **路径**: `/v2/products/stocks`
- **方法**: `POST`
- **描述**: 可以改变一个商品的库存数量信息。   转交库存数量是当前可用库存，不包括已预留库存。在更新库存之前，请使用以下方法检查已预留库存数量：/v1/product/info/stocks-by-warehouse/fbs。   在一次查询中最多可以改变100个商品。从一个卖家账号每分钟可以发送不超过80次...
- **操作ID**: `ProductAPI_ProductsStocksV2`

#### 2. 关于商品数量的信息

- **路径**: `/v4/product/info/stocks`
- **方法**: `POST`
- **描述**: 返回关于 FBS 和 rFBS 方案下商品数量的信息:   - 有多少现货。   - 给买家保留了多少。
- **操作ID**: `ProductAPI_GetProductInfoStocks`

#### 3. 关于卖家库存余额的信息

- **路径**: `/v1/product/info/stocks-by-warehouse/fbs`
- **方法**: `POST`
- **操作ID**: `ProductAPI_ProductStocksByWarehouseFbs`

#### 4. 更新价格

- **路径**: `/v1/product/import/prices`
- **方法**: `POST`
- **描述**: 允许更改一个或多个商品的价格。每个商品的价格每小时不能更新超过10次。要重置`old_price`，请将此参数设为0。  如果请求中同时包含参数 `offer_id` 和 `product_id`，系统将优先根据 `offer_id` 应用更改。为避免歧义，建议仅使用一个参数。
- **操作ID**: `ProductAPI_ImportProductsPrices`

#### 5. 获取商品价格信息

- **路径**: `/v5/product/info/prices`
- **方法**: `POST`
- **操作ID**: `ProductAPI_GetProductInfoPrices`

#### 6. 通过减价商品的SKU查找减价商品和主商品的信息

- **路径**: `/v1/product/info/discounted`
- **方法**: `POST`
- **描述**: 一种通过SKU获取打折商品的状况和缺陷信息的方法。该方法还返回主商品的SKU。
- **操作ID**: `ProductAPI_GetProductInfoDiscounted`

#### 7. 为打折商品设置折扣

- **路径**: `/v1/product/update/discount`
- **方法**: `POST`
- **描述**: FBS模式下出售折扣商品的折扣幅度设置方法。
- **操作ID**: `ProductAPI_ProductUpdateDiscount`

---

### 🔹 促销活动

#### 1. 活动清单

- **路径**: `/v1/actions`
- **方法**: `GET`
- **描述**: 用于获取可参与的 Ozon 促销活动列表。  [了解更多关于 Ozon 促销活动的信息](https://docs.ozon.ru/global/zh/promotion/big-promotions/rasprodazha/)
- **操作ID**: `Promos`

#### 2. 可用的促销商品清单

- **路径**: `/v1/actions/candidates`
- **方法**: `POST`
- **描述**: 通过识别号获取可参与促销活动的商品清单的方法。    自2025年5月5日起，offset分页参数将被关闭。请切换到 last_id 参数。  
- **操作ID**: `PromosCandidates`

#### 3. 参与 活动的商品列表

- **路径**: `/v1/actions/products`
- **方法**: `POST`
- **描述**: 一种按识别号检索参加促销活动的商品清单的方法。    自2025年5月5日起，offset分页参数将被关闭。请切换到 last_id 参数。  
- **操作ID**: `PromosProducts`

#### 4. 在促销活动中增加一个商品

- **路径**: `/v1/actions/products/activate`
- **方法**: `POST`
- **描述**: 一种向现有促销活动添加商品的方法。
- **操作ID**: `PromosProductsActivate`

#### 5. 从活动中删除商品

- **路径**: `/v1/actions/products/deactivate`
- **方法**: `POST`
- **描述**: 一种从活动中移除商品的方法。
- **操作ID**: `PromosProductsDeactivate`

#### 6. 申请折扣列表

- **路径**: `/v1/actions/discounts-task/list`
- **方法**: `POST`
- **描述**: 获取买家希望从卖家那里获得折扣的商品列表方法。
- **操作ID**: `promos_task_list`

#### 7. 同意折扣申请

- **路径**: `/v1/actions/discounts-task/approve`
- **方法**: `POST`
- **描述**: 您可以同意处于以下状态的申请：`NEW` — 新的， `SEEN` — 已查看的。
- **操作ID**: `promos_task_approve`

#### 8. 取消折扣申请

- **路径**: `/v1/actions/discounts-task/decline`
- **方法**: `POST`
- **描述**: 您可以取消处于以下状态的申请： `NEW` — 新的, `SEEN` — 已查看的。
- **操作ID**: `promos_task_decline`

---

### 🔹 定价策略

#### 1. 竞争对手名单

- **路径**: `/v1/pricing-strategy/competitors/list`
- **方法**: `POST`
- **描述**: 获取竞争对手列表的方法 - 在其他在线商店和电商平台上拥有类似商品的卖家。
- **操作ID**: `pricing_competitors`

#### 2. 策略列表

- **路径**: `/v1/pricing-strategy/list`
- **方法**: `POST`
- **操作ID**: `pricing_list`

#### 3. 创建策略

- **路径**: `/v1/pricing-strategy/create`
- **方法**: `POST`
- **操作ID**: `pricing_create`

#### 4. 策略信息

- **路径**: `/v1/pricing-strategy/info`
- **方法**: `POST`
- **操作ID**: `pricing_info`

#### 5. 更新策略

- **路径**: `/v1/pricing-strategy/update`
- **方法**: `POST`
- **描述**: 可以更新除系统策略之外的所有策略。
- **操作ID**: `pricing_update`

#### 6. 将商品添加到策略

- **路径**: `/v1/pricing-strategy/products/add`
- **方法**: `POST`
- **操作ID**: `pricing_items-add`

#### 7. 策略ID列表

- **路径**: `/v1/pricing-strategy/strategy-ids-by-product-ids`
- **方法**: `POST`
- **操作ID**: `pricing_ids`

#### 8. 策略中的商品列表

- **路径**: `/v1/pricing-strategy/products/list`
- **方法**: `POST`
- **操作ID**: `pricing_items-list`

#### 9. 竞争对手  的商品价格

- **路径**: `/v1/pricing-strategy/product/info`
- **方法**: `POST`
- **描述**: 如果您向定价策略添加了商品，那么方式将恢复价格和竞争对手商品链接。
- **操作ID**: `pricing_items-info`

#### 10. 从策略中删除商品

- **路径**: `/v1/pricing-strategy/products/delete`
- **方法**: `POST`
- **操作ID**: `pricing_items-delete`

#### 11. 更改策略状态

- **路径**: `/v1/pricing-strategy/status`
- **方法**: `POST`
- **描述**: 可以更改除系统策略之外的任何策略的状态。
- **操作ID**: `pricing_status`

#### 12. 删除策略

- **路径**: `/v1/pricing-strategy/delete`
- **方法**: `POST`
- **描述**: 可以删除除系统策略之外的任何策略。
- **操作ID**: `pricing_delete`

---

### 🔹 仓库

#### 1. 仓库清单

- **路径**: `/v1/warehouse/list`
- **方法**: `POST`
- **描述**: 方法返回 FBS 和 rFBS 仓库列表。如需获取 FBO 仓库列表，请使用方法[/v1/cluster/list](#operation/SupplyDraftAPI_DraftClusterList)。
- **操作ID**: `WarehouseAPI_WarehouseList`

#### 2. 仓库物流方式清单

- **路径**: `/v1/delivery-method/list`
- **方法**: `POST`
- **操作ID**: `WarehouseAPI_DeliveryMethodList`

---

### 🔹 多边形

#### 1. 创建一个快递的设施

- **路径**: `/v1/polygon/create`
- **方法**: `POST`
- **描述**: 你可以为快递方式添加一个设施。  通过在https://geojson.io，获取其坐标来创建一个设施：在地图上至少标记3个点，并用线连接起来。
- **操作ID**: `PolygonAPI_CreatePolygon`

#### 2. 将快递方式与快递设施联系起来

- **路径**: `/v1/polygon/bind`
- **方法**: `POST`
- **操作ID**: `PolygonAPI_BindPolygon`

---

### 🔹 FBS/rFBS标志代码和营销管理

#### 1. 获取商品实例信息

- **路径**: `/v5/fbs/posting/product/exemplar/create-or-get`
- **方法**: `POST`
- **描述**: 该方法用于获取货件中商品的实例信息。  请使用此方法获取`exemplar_id`。
- **操作ID**: `PostingAPI_FbsPostingProductExemplarCreateOrGet`

#### 2. 标志代码验证

- **路径**: `/v4/fbs/posting/product/exemplar/validate`
- **方法**: `POST`
- **描述**: 用于检查代码是否符合数量和字符组成的要求。 [查看卖家知识库中的错误详情](https://seller-edu.ozon.ru/fbs/ozon-logistika/markirovka#какие-могут-возникать-ошибки-при-проверке-кода-маркиров...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarValidate`

#### 3. 检查并保存份数数据

- **路径**: `/v4/fbs/posting/product/exemplar/set`
- **方法**: `POST`
- **描述**:    将来该方式将被关闭。请转至 /v5/fbs/posting/product/exemplar/set.   异步方法： - 检查在“诚信标志”系统中流通份数的存在性； - 保存份数数据。  为了获取已创建样件的数据，请使用 [/v5/fbs/posting/product/exemplar/c...
- **操作ID**: `PostingAPI_SetProductExemplar`

#### 4. 检查并保存份数数据 (第5方案)

- **路径**: `/v5/fbs/posting/product/exemplar/set`
- **方法**: `POST`
- **描述**: 异步方法： - 检查在“诚信标志”系统中流通份数的存在性； - 保存份数数据。  为了获取已创建样件的数据，请使用 [/v5/fbs/posting/product/exemplar/create-or-get](#operation/PostingAPI_FbsPostingProductExem...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarSet`

#### 5. 获取样件添加状态

- **路径**: `/v4/fbs/posting/product/exemplar/status`
- **方法**: `POST`
- **描述**: 获取在[/v5/fbs/posting/product/exemplar/set](#operation/PostingAPI_FbsPostingProductExemplarSet)方式中传输的样件添加状态的方式。 同时还归还这些样件的数据。
- **操作ID**: `PostingAPI_GetProductExemplarStatus`

#### 6. 搜集订单 (第4方案)

- **路径**: `/v4/posting/fbs/ship`
- **方法**: `POST`
- **描述**: 拆分订单，并将状态改为`awaiting_deliver`。  `packages`中的每个元素都可以包含多个`products`和货物。 `products`中的每个元素是包含在这批货物中的商品。  如果出现以下情况，需要拆分订单：   - 商品在一个包装里放不下，   - 商品不可以放在一个包装...
- **操作ID**: `PostingAPI_ShipFbsPostingV4`

#### 7. 货件的部分装配 (第4方案)

- **路径**: `/v4/posting/fbs/ship/package`
- **方法**: `POST`
- **描述**: 如果在请求中转交货件中的部分商品，那么方式将把最初的货件分为两个部分。在第一个未完成备货的货件中将剩下请求中没有转交的那一部分商品。   默认情况下，创建的货件状态为`awaiting_packaging`（等待备货）。 最初的货件状态将仅在它分成的货件状态发生变化后才发生变化。
- **操作ID**: `PostingAPI_ShipFbsPostingPackage`

---

### 🔹 FBS配送

#### 1. 运输信息

- **路径**: `/v1/carriage/get`
- **方法**: `POST`
- **操作ID**: `CarriageGet`

#### 2. 可供运输的列表

- **路径**: `/v1/posting/carriage-available/list`
- **方法**: `POST`
- **描述**: 需要打印验收证明书和运输货单的收货方式。
- **操作ID**: `PostingAPI_GetCarriageAvailableList`

---

### 🔹 rFBS配送

#### 1. 将状态改成“运输中”

- **路径**: `/v2/fbs/posting/delivering`
- **方法**: `POST`
- **描述**: 如果使用第三方快递服务，请将货运状态改为“运输中”。
- **操作ID**: `PostingAPI_FbsPostingDelivering`

#### 2. 添加跟踪号

- **路径**: `/v2/fbs/posting/tracking-number/set`
- **方法**: `POST`
- **描述**: 为货件添加跟踪号。每次最多可添加20个跟踪号。
- **操作ID**: `PostingAPI_FbsPostingTrackingNumberSet`

#### 3. 状态改为“最后一英里”

- **路径**: `/v2/fbs/posting/last-mile`
- **方法**: `POST`
- **描述**: 如果使用第三方快递服务，请将货运状态改为“最后一英里”。
- **操作ID**: `PostingAPI_FbsPostingLastMile`

#### 4. 将状态改成“已送达”

- **路径**: `/v2/fbs/posting/delivered`
- **方法**: `POST`
- **描述**: 如果使用第三方快递服务，请将货运状态改成“已送达”。
- **操作ID**: `PostingAPI_FbsPostingDelivered`

#### 5. 将状态改为“由卖家发送”

- **路径**: `/v2/fbs/posting/sent-by-seller`
- **方法**: `POST`
- **描述**: 将货运状态改为“由卖家发送”。该状态仅适用于从国外销售的头程物流卖家。
- **操作ID**: `PostingAPI_FbsPostingSentbyseller`

#### 6. 确认货件发运日期

- **路径**: `/v1/posting/cutoff/set`
- **方法**: `POST`
- **描述**: 用于卖家或非集成运输商配送的货件方法。
- **操作ID**: `PostingAPI_SetPostingCutoff`

---

### 🔹 通行证

#### 1. 通行证列表

- **路径**: `/v1/pass/list`
- **方法**: `POST`
- **操作ID**: `PassList`

#### 2. 创建通行证

- **路径**: `/v1/carriage/pass/create`
- **方法**: `POST`
- **描述**: 创建的通行证ID将添加到运输中。
- **操作ID**: `carriagePassCreate`

#### 3. 更新通行证

- **路径**: `/v1/carriage/pass/update`
- **方法**: `POST`
- **操作ID**: `carriagePassUpdate`

#### 4. 删除通行证

- **路径**: `/v1/carriage/pass/delete`
- **方法**: `POST`
- **操作ID**: `carriagePassDelete`

#### 5. 创建退货通行证

- **路径**: `/v1/return/pass/create`
- **方法**: `POST`
- **操作ID**: `returnPassCreate`

#### 6. 更新退货通行证

- **路径**: `/v1/return/pass/update`
- **方法**: `POST`
- **操作ID**: `returnPassUpdate`

#### 7. 删除退货通行证

- **路径**: `/v1/return/pass/delete`
- **方法**: `POST`
- **操作ID**: `returnPassDelete`

#### 8. FBS退货数量

- **路径**: `/v1/returns/company/fbs/info`
- **方法**: `POST`
- **描述**: 获取FBS退货及其数量的信息的方法。
- **操作ID**: `returnsCompanyFBSInfo`

---

### 🔹 FBO和rFBS商品集装

#### 1. 未处理货件列表 （第三 版）

- **路径**: `/v3/posting/fbs/unfulfilled/list`
- **方法**: `POST`
- **描述**: 返回指定时间段的未处理货件列表 —— 不应超过一年。  可能的货件运输状态： - `awaiting_registration` — 等待注册， - `acceptance_in_progress` — 正在验收， - `awaiting_approve` — 等待确认， - `awaiting_p...
- **操作ID**: `PostingAPI_GetFbsPostingUnfulfilledList`

#### 2. 货件列表（第三版）

- **路径**: `/v3/posting/fbs/list`
- **方法**: `POST`
- **描述**: 返回指定时间段的货运列表-不应超过一年。  此外，您还可以按货件状态过滤货件。  `has_next = true` 在响应中表示，不是所有的货物数组都被返回。要获取有关剩余货件的信息，请提出新的含 `offset`值的请求。
- **操作ID**: `PostingAPI_GetFbsPostingListV3`

#### 3. 按照ID获取货件信息

- **路径**: `/v3/posting/fbs/get`
- **方法**: `POST`
- **操作ID**: `PostingAPI_GetFbsPostingV3`

#### 4. 按条形码获取有关货件的信息

- **路径**: `/v2/posting/fbs/get-by-barcode`
- **方法**: `POST`
- **操作ID**: `PostingAPI_GetFbsPostingByBarcode`

#### 5. 可用产地名单

- **路径**: `/v2/posting/fbs/product/country/list`
- **方法**: `POST`
- **描述**: 获取可用产地及其ISO代码列表的方法。
- **操作ID**: `PostingAPI_ListCountryProductFbsPostingV2`

#### 6. 添加商品产地信息

- **路径**: `/v2/posting/fbs/product/country/set`
- **方法**: `POST`
- **描述**: 将“产地”商品属性添加到方法中，如果该信息未指定。
- **操作ID**: `PostingAPI_SetCountryProductFbsPostingV2`

#### 7. 打印标签

- **路径**: `/v2/posting/fbs/package-label`
- **方法**: `POST`
- **描述**: 生成带有指定货件标签的PDF文件。 在一个请求中最多可以传递20个ID。 如果至少有一个货件发生错误，则不会为请求中的所有货件准备标签。  我们建议在订单装配后45-60秒内询问标签。  错误 `The next postings aren't ready` 标识，未备好标签，请稍后重试。
- **操作ID**: `PostingAPI_PostingFBSPackageLabel`

#### 8. 货件装运

- **路径**: `/v2/posting/fbs/awaiting-delivery`
- **方法**: `POST`
- **描述**: 将有争议的订单转到装运。货件状态将更改为 `awaiting_deliver`。
- **操作ID**: `PostingAPI_MoveFbsPostingToAwaitingDelivery`

#### 9. 货件取消原因

- **路径**: `/v2/posting/fbs/cancel-reason/list`
- **方法**: `POST`
- **描述**: 返回所有货件取消原因列表。
- **操作ID**: `PostingAPI_GetPostingFbsCancelReasonList`

#### 10. 货运取消原因

- **路径**: `/v1/posting/fbs/cancel-reason`
- **方法**: `POST`
- **描述**: 返回特定货件的取消原因列表。
- **操作ID**: `PostingAPI_GetPostingFbsCancelReasonV1`

#### 11. 取消货运

- **路径**: `/v2/posting/fbs/cancel`
- **方法**: `POST`
- **描述**: 将装运状态改为 `cancelled`。  如果您使用 rFBS 模式, 可用以下取消原因ID — `cancel_reason_id`:  - `352` — 商品无库存； - `400` — 只剩下有缺陷的商品。 - `401` — 仲裁取消； - `402` — 其他原因； - `665` —...
- **操作ID**: `PostingAPI_CancelFbsPosting`

#### 12. 为货件中的称重商品添加重量

- **路径**: `/v2/posting/fbs/product/change`
- **方法**: `POST`
- **操作ID**: `PostingAPI_ChangeFbsPostingProduct`

#### 13. 取消某些商品发货

- **路径**: `/v2/posting/fbs/product/cancel`
- **方法**: `POST`
- **描述**: 如果您无法从货件中发送部分产品，请使用该方法。  为了在使用FBS或rFBS模式时获取取消原因的标识符`cancel_reason_id`，请使用方法[/v2/posting/fbs/cancel-reason/list](#operation/PostingAPI_GetPostingFbsCan...
- **操作ID**: `PostingAPI_CancelFbsPostingProduct`

---

### 🔹 退货

#### 1. FBO和FBS退货信息

- **路径**: `/v1/returns/list`
- **方法**: `POST`
- **描述**: 用于获取 FBO 和 FBS 退货信息的方法。
- **操作ID**: `returnsList`

---

### 🔹 取消订单

#### 1. 获取有关rFBS取消订单的消息

- **路径**: `/v1/conditional-cancellation/get`
- **方法**: `POST`
- **描述**:  2025年8月3日，旧方法将被停用。请切换到/v2/conditional-cancellation/list。   获取有关rFBS取消订单消息的方法。
- **操作ID**: `CancellationAPI_GetConditionalCancellation`

#### 2. 获取 rFBS 取消申请列表

- **路径**: `/v2/conditional-cancellation/list`
- **方法**: `POST`
- **描述**: 用于获取 rFBS 订单取消申请列表的方法。
- **操作ID**: `CancellationAPI_GetConditionalCancellationListV2`

#### 3. 获取rFBS取消申请列表

- **路径**: `/v1/conditional-cancellation/list`
- **方法**: `POST`
- **描述**:  2025年8月3日，旧方法将被停用。请切换到/v2/conditional-cancellation/list。   获取rFBS取消申请列表的方法。
- **操作ID**: `CancellationAPI_GetConditionalCancellationList`

#### 4. 确认 rFBS 取消申请

- **路径**: `/v2/conditional-cancellation/approve`
- **方法**: `POST`
- **描述**: 此方法可将状态为 `ON_APPROVAL` 的取消申请标记为已确认。订单将被取消，款项退还给买家。
- **操作ID**: `CancellationAPI_ConditionalCancellationApproveV2`

#### 5. 确定rFBS取消订单

- **路径**: `/v1/conditional-cancellation/approve`
- **方法**: `POST`
- **描述**:  2025年8月3日，旧方法将被停用。请切换到/v2/conditional-cancellation/approve。   该方法允许您在 `ON_APPROVAL` 状态下批准取消订单的申请。 该方法适用于 rFBS 订单。 订单将被取消，款项将退还给买家。
- **操作ID**: `CancellationAPI_ConditionalCancellationApprove`

#### 6. 拒绝 rFBS 取消申请

- **路径**: `/v2/conditional-cancellation/reject`
- **方法**: `POST`
- **描述**: 此方法可拒绝状态为 `ON_APPROVAL` 的取消申请。在 `comment` 参数中说明拒绝原因。订单将保留当前状态，并需继续发货给买家。
- **操作ID**: `CancellationAPI_ConditionalCancellationRejectV2`

#### 7. 拒绝取消rFBS申请

- **路径**: `/v1/conditional-cancellation/reject`
- **方法**: `POST`
- **描述**:  2025年8月3日，旧方法将被停用。请切换到/v2/conditional-cancellation/reject。   该方法允许您拒绝处于 `ON_APPROVAL`状态的取消申请。该方法适用于 rFBS 订单。 请在 `comment`参数中解释您的决定。  订单将保持相同的状态，并且交付给...
- **操作ID**: `CancellationAPI_ConditionalCancellationReject`

---

### 🔹 rFBS商品集装

#### 1. 退货申请列表

- **路径**: `/v2/returns/rfbs/list`
- **方法**: `POST`
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsListV2`

#### 2. 退货申请信息

- **路径**: `/v2/returns/rfbs/get`
- **方法**: `POST`
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsGetV2`

#### 3. 拒绝退货申请

- **路径**: `/v2/returns/rfbs/reject`
- **方法**: `POST`
- **描述**:  将来该方式将被关闭。请转至 /v1/returns/rfbs/action/set。   该方法允许拒绝rFBS订单的退货申请。您可以在 `comment` 参数中解释您的决定。
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsRejectV2`

#### 4. 退还部分商品金额

- **路径**: `/v2/returns/rfbs/compensate`
- **方法**: `POST`
- **描述**:  将来该方式将被关闭。请转至 /v1/returns/rfbs/action/set。   用于部分赔偿商品金额的方法：您退还部分款项给买家，商品则留在买家手中。
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsCompensateV2`

#### 5. 批准退货申请

- **路径**: `/v2/returns/rfbs/verify`
- **方法**: `POST`
- **描述**:  将来该方式将被关闭。请转至 /v1/returns/rfbs/action/set。   该方法允许批准申请并同意接收商品进行检查。  请使用[/v2/returns/rfbs/receive-return](#operation/RFBSReturnsAPI_ReturnsRfbsReceive...
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsVerifyV2`

#### 6. 确认收到待检查商品

- **路径**: `/v2/returns/rfbs/receive-return`
- **方法**: `POST`
- **描述**:    将来该方式将被关闭。请转至 /v1/returns/rfbs/action/set。 
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsReceiveReturnV2`

#### 7. 向买家退款

- **路径**: `/v2/returns/rfbs/return-money`
- **方法**: `POST`
- **描述**:  将来该方式将被关闭。请转至 /v1/returns/rfbs/action/set。   该方法确认退还商品的全额。 如果您同意以下条件，请使用此方法： - 立即退还商品金额并将其留给买家； - 在收到并检查商品后退还金额。  如果商品有瑕疵或质量问题，请在赔偿栏中填写运费金额，然后点击发送。
- **操作ID**: `RFBSReturnsAPI_ReturnsRfbsReturnMoneyV2`

#### 8. 传递 rFBS  退货的可用操作

- **路径**: `/v1/returns/rfbs/action/set`
- **方法**: `POST`
- **描述**: 用于传递  rFBS 退货操作的方法。
- **操作ID**: `ReturnsAPI_ReturnsRfbsActionSet`

---

### 🔹 与买家的聊天

#### 1. 发送信息

- **路径**: `/v1/chat/send/message`
- **方法**: `POST`
- **描述**: 通过识别码向现有聊天发送消息。
- **操作ID**: `ChatAPI_ChatSendMessage`

#### 2. 发送文件

- **路径**: `/v1/chat/send/file`
- **方法**: `POST`
- **描述**: 通过文件的识别码将文件发送到现有的聊天。
- **操作ID**: `ChatAPI_ChatSendFile`

#### 3. 创建新聊天

- **路径**: `/v1/chat/start`
- **方法**: `POST`
- **描述**: 与买家建立关于的快递的新聊天。例如，为了确定地址或商品型号。
- **操作ID**: `ChatAPI_ChatStart`

#### 4. 聊天清单

- **路径**: `/v2/chat/list`
- **方法**: `POST`
- **描述**: 根据指定的过滤器发回关于聊天的信息。
- **操作ID**: `ChatAPI_ChatListV2`

#### 5. 聊天历史记录

- **路径**: `/v2/chat/history`
- **方法**: `POST`
- **描述**:  2025年7月13日，旧方法将被停用。请切换到/v3/chat/history。    恢复聊天室消息历史记录。默认顺序为从最新消息到之前的消息。
- **操作ID**: `ChatAPI_ChatHistoryV2`

#### 6. 聊天历史记录

- **路径**: `/v3/chat/history`
- **方法**: `POST`
- **描述**: 恢复聊天室消息历史记录。默认顺序为从最新消息到之前的消息。
- **操作ID**: `ChatAPI_ChatHistoryV3`

#### 7. 将信息标记为已读

- **路径**: `/v2/chat/read`
- **方法**: `POST`
- **描述**: 一种将选定的信息和其之前的信息标记为已读的方法。
- **操作ID**: `ChatAPI_ChatReadV2`

---

### 🔹 报告

#### 1. 报告信息

- **路径**: `/v1/report/info`
- **方法**: `POST`
- **描述**: 通过识别码回送有关先前创建的报告的信息。
- **操作ID**: `ReportAPI_ReportInfo`

#### 2. 报告清单

- **路径**: `/v1/report/list`
- **方法**: `POST`
- **描述**: 回送之前已经生成的报告的列表。
- **操作ID**: `ReportAPI_ReportList`

#### 3. 商品报告

- **路径**: `/v1/report/products/create`
- **方法**: `POST`
- **描述**: 获得带有商品数据的报告的方法。例如，Ozon的ID，商品的数量，价格，状态。 与个人中心中的**商品和价格→商品列表→下载商品CSV**部分相符。  一些空白的解释：   - __Ozon Product ID__ — 我们系统中的卖家系统中的商品标识符 — `product_id`。例如，如果你从...
- **操作ID**: `ReportAPI_CreateCompanyProductsReport`

#### 4. 发货报告

- **路径**: `/v1/report/postings/create`
- **方法**: `POST`
- **描述**: 带有订单信息的发货报告：   - 订单状态，   - 处理的开始日期，   - 订单号，   - 发货号码，   - 发货费用，   - 发货内容。 与个人中心中的**FBO→来自Ozon仓库的订单**和**FBS→来自我的仓库的订单→CSV**部分相符。
- **操作ID**: `ReportAPI_CreateCompanyPostingsReport`

#### 5. 财务报告

- **路径**: `/v1/finance/cash-flow-statement/list`
- **方法**: `POST`
- **描述**: 从1号到15号以及从16号到31号的财务报告获取方式。 在请求一天的报告时，您将收到15天的报告。 与个人中心中的**财务→报告列表**部分相符。
- **操作ID**: `FinanceAPI_FinanceCashFlowStatementList`

#### 6. 减价商品报告

- **路径**: `/v1/report/discounted/create`
- **方法**: `POST`
- **描述**: 开始生成关于Ozon仓库中打折商品的报告。 Ozon可以自行处理一个商品，例如，如果它被损坏了。  请求结果将不是报告本身，而是其唯一的识别码。 要获取报告，请在 [/v1/report/info](#operation/ReportAPI_ReportInfo) 方法请求中发送ID。  从一个卖家...
- **操作ID**: `ReportAPI_CreateDiscountedReport`

#### 7. 关于FBS仓库库存报告

- **路径**: `/v1/report/warehouse/stock`
- **方法**: `POST`
- **描述**: 报告包含仓库中可用和预留的商品数量的信息。 与个人中心中的**FBO→物流管理→库存管理→以XLS格式下载**部分相符。  查询的结果不是报告本身，而是其唯一ID。要获取报告，请在 [/v1/report/info](#operation/ReportAPI_ReportInfo) 方法的请求中发送...
- **操作ID**: `ReportAPI_CreateStockByWarehouseReport`

---

### 🔹 分析报告

#### 1. 分析数据

- **路径**: `/v1/analytics/data`
- **方法**: `POST`
- **描述**: 请指定需要计算的时间段和指标。响应将包含按`dimensions`参数分组的分析。   从一个卖家账号每分钟可以发送1次请求。 与个人中心中的**分析→图表**部分相符。
- **操作ID**: `AnalyticsAPI_AnalyticsGetData`

#### 2. 获取商品搜索查询信息

- **路径**: `/v1/analytics/product-queries`
- **方法**: `POST`
- **描述**: 使用该方法可以获取您的商品在 Ozon 平台上的搜索查询信息。完整分析数据仅适用于[Premium](https://seller-edu.ozon.ru/seller-rating/about-rating/premium-program) 和 [Premium Plus](https://sel...
- **操作ID**: `AnalyticsAPI_AnalyticsProductQueries`

#### 3. 有关特定商品查询的信息

- **路径**: `/v1/analytics/product-queries/details`
- **方法**: `POST`
- **描述**: 使用该方法获取特定商品的查询数据。只有 [Premium](https://seller-edu.ozon.ru/seller-rating/about-rating/premium-program) 或 [Premium Plus](https://seller-edu.ozon.ru/selle...
- **操作ID**: `AnalyticsAPI_AnalyticsProductQueriesDetails`

---

### 🔹 财务报告

#### 1. 商品销售报告 （第2版）

- **路径**: `/v2/finance/realization`
- **方法**: `POST`
- **描述**: 当月与交付和退货有关的销售情况。订单取消与非赎回不包括其中。 与个人中心中的**财务→文件→销售报告→商品销售报告**部分相符。  报告将最迟于下个月的第五天发送。
- **操作ID**: `FinanceAPI_GetRealizationReportV2`

#### 2. 按订单细分的商品销售报告

- **路径**: `/v1/finance/realization/posting`
- **方法**: `POST`
- **描述**: 已送达和已退回商品销售的报告，带有每笔订单的详细信息。不包括取消和无人认领的订单。从现在起至2023年8月的报告可供您使用。
- **操作ID**: `FinanceAPI_GetRealizationReportV1`

#### 3. 交易清单

- **路径**: `/v3/finance/transaction/list`
- **方法**: `POST`
- **描述**:  请使用顺序发送请求的方式。   返回所有应计项目的详细信息。 在一次请求中可获取信息的最长时间为1月。  如果请求中未指出 `posting_number`, 那么响应将包含指定时间段内的所有订单或特定订单类型。 与个人中心中的**财务→应计费用**部分相符。
- **操作ID**: `FinanceAPI_FinanceTransactionListV3`

#### 4. 清单数目

- **路径**: `/v3/finance/transaction/totals`
- **方法**: `POST`
- **描述**: 返回指定时间的清单总数。 与个人中心中的**财务→应计费用→总金额横幅**部分相符。
- **操作ID**: `FinanceAPI_FinanceTransactionTotalV3`

#### 5. 每日商品销售报告

- **路径**: `/v1/finance/realization/by-day`
- **方法**: `POST`
- **描述**: 该方法返回每日[商品销售报告](#operation/FinanceAPI_GetRealizationReportV2)中的销售金额数据。不包括取消和无人认领的订单。数据仅可获取从当前日期起最多32个自然日之内的记录。此方法仅对[Premium Plus](https://seller-edu.o...
- **操作ID**: `FinanceAPI_GetRealizationByDayReportV1`

#### 6. 赔偿报告

- **路径**: `/v1/finance/compensation`
- **方法**: `POST`
- **描述**: 用于获取赔偿报告的方法。与卖家个人中心中 **财务 → 文件 → 赔偿及其他应计费用** 部分的报告一致。
- **操作ID**: `ReportAPI_GetCompensationReport`

#### 7. 赔偿返还报告

- **路径**: `/v1/finance/decompensation`
- **方法**: `POST`
- **描述**: 用于获取赔偿返还报告的方法。与卖家个人中心中 **财务 → 文件 → 赔偿及其他应计费用** 部分的报告一致。
- **操作ID**: `ReportAPI_GetDecompensationReport`

---

### 🔹 测试

#### 1. 将订单拆分为不带备货的货件

- **路径**: `/v1/posting/fbs/split`
- **方法**: `POST`
- **描述**: 您可以在 [讨论](https://dev.ozon.ru/community/1068-Razdelenie-otpravleniia-na-neskolko) 的评论中对此方法提供反馈 在 Ozon for dev 开发者社区中。
- **操作ID**: `FbsSplit`

#### 2. 获取平均配送时间的分析数据

- **路径**: `/v1/analytics/average-delivery-time`
- **方法**: `POST`
- **描述**: 该方法可获取商品配送到买家的平均时间分析。对应卖家个人中心**分析→ 销售地理→ 平均配送时间**模块。每个集群的详细分析可通过方法[/v1/analytics/average-delivery-time/details](#operation/AnalyticsAPI_AverageDeliver...
- **操作ID**: `AnalyticsAPI_AverageDeliveryTime`

#### 3. 获取平均配送时间的详细分析

- **路径**: `/v1/analytics/average-delivery-time/details`
- **方法**: `POST`
- **描述**: 本方法对应卖家个人中心的**分析 → 配送范围 → 平均配送时间**模块。  如需获取各集群的总体分析，请使用方法/v1/analytics/average-delivery-time。  您可以在 [讨论](https://dev.ozon.ru/community/1421-Novye-meto...
- **操作ID**: `AnalyticsAPI_AverageDeliveryTimeDetails`

#### 4. 使用API密钥获取角色和方式列表

- **路径**: `/v1/roles`
- **方法**: `POST`
- **描述**: 获取与API密钥绑定的角色和方式信息的方式。  您可以在 Ozon for dev 开发者社区的[讨论区](https://dev.ozon.ru/community/1609-Novyi-metod-dlia-polucheniia-rolei-po-API-kliuchu)对该方法留下反馈。
- **操作ID**: `AccessAPI_RolesByToken`

#### 5. 检查并保存份数数据

- **路径**: `/v6/fbs/posting/product/exemplar/set`
- **方法**: `POST`
- **描述**: 异步方法： - 检查在“诚信标志”系统中流通份数的存在性； - 保存份数数据。  为了获取已创建样件的数据，请使用 [/v6/fbs/posting/product/exemplar/create-or-get](#operation/PostingAPI_FbsPostingProductExem...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarSetV6`

#### 6. 获取已创建样件数据

- **路径**: `/v6/fbs/posting/product/exemplar/create-or-get`
- **方法**: `POST`
- **描述**: 此方法用于获取货件中商品的信息，这些信息通过方法 [/v6/fbs/posting/product/exemplar/set](#operation/PostingAPI_FbsPostingProductExemplarSetV6) 传递。  请使用此方法获取 `exemplar_id`。  您可...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarCreateOrGetV6`

#### 7. 获取样件添加状态

- **路径**: `/v5/fbs/posting/product/exemplar/status`
- **方法**: `POST`
- **描述**: 获取在 [/v6/fbs/posting/product/exemplar/set](PostingAPI_FbsPostingProductExemplarSetV6) 方式中传输的样件添加状态的方式。  同时还归还这些样件的数据。  您可以在 [讨论](https://dev.ozon.ru/c...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarStatusV5`

#### 8. 标志代码验证

- **路径**: `/v5/fbs/posting/product/exemplar/validate`
- **方法**: `POST`
- **描述**: 这是一种验证代码是否符合“诚信标志”系统对字符数量和组成方面要求的方式。  如果您没有货物报关单号，那么您可以不输入。  您可以在 [讨论](https://dev.ozon.ru/community/1269-Metody-dlia-raboty-so-spiskom-markirovok-FBS...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarValidateV5`

#### 9. Обновить данные экземпляров

- **路径**: `/v1/fbs/posting/product/exemplar/update`
- **方法**: `POST`
- **描述**: 请使用 [/v6/fbs/posting/product/exemplar/set](#operation/PostingAPI_FbsPostingProductExemplarSetV6), 方法，在传输实例数据后调用该方法，以保存“等待发运”状态下订单的最新实例数据。  您可以在 [讨论](h...
- **操作ID**: `PostingAPI_FbsPostingProductExemplarUpdate`

#### 10. 体积重量特征不正确的商品列表

- **路径**: `/v1/product/info/wrong-volume`
- **方法**: `POST`
- **描述**: 返回体积重量特征不正确的商品列表。如果您已正确填写尺寸，请联系Ozon客服。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1260-Informer-nekorrektnykh-OVKh)区中，留下对此方法的反馈。
- **操作ID**: `ProductAPI_ProductInfoWrongVolume`

---

### 🔹 评价

#### 1. 对评价留下评论

- **路径**: `/v1/review/comment/create`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。
- **操作ID**: `ReviewAPI_CommentCreate`

#### 2. 删除对评价的评论

- **路径**: `/v1/review/comment/delete`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。
- **操作ID**: `ReviewAPI_CommentDelete`

#### 3. 评价的评论列表

- **路径**: `/v1/review/comment/list`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。  该方法返回已通过审核的评价...
- **操作ID**: `ReviewAPI_CommentList`

#### 4. 更改评价状态

- **路径**: `/v1/review/change-status`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。
- **操作ID**: `ReviewAPI_ReviewChangeStatus`

#### 5. 根据状态统计的评价数量

- **路径**: `/v1/review/count`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。
- **操作ID**: `ReviewAPI_ReviewCount`

#### 6. 获取评价信息

- **路径**: `/v1/review/info`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。
- **操作ID**: `ReviewAPI_ReviewInfo`

#### 7. 获取评价列表

- **路径**: `/v1/review/list`
- **方法**: `POST`
- **描述**: 仅适用于拥有 Premium Plus 订阅的卖家。  您可以在开发者社区 Ozon for dev 的[讨论](https://dev.ozon.ru/community/1190-Metody-dlia-raboty-s-otzyvami)区中，留下对此方法的反馈。  该方法不会返回商品评价中的...
- **操作ID**: `ReviewAPI_ReviewList`

---
